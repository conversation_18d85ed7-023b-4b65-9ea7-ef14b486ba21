# Legal Rules Coverage Audit Implementation Plan

## Phase 1: Build Canonical Checklist (2-3 weeks)

### Texas Sources to Audit
- [ ] **Criminal**: Code of Criminal Procedure Ch. 15-33, 41-44, Art. 17, Art. 24
- [ ] **Civil**: Texas Rules of Civil Procedure Ch. 1-371 + Civ. Prac. & Rem. Code §16
- [ ] **Family**: TX Family Code Titles 1-5 + TRCP overlays
- [ ] **Local Rules**: 11 largest counties (Harris, Dallas, Tarrant, Bexar, Travis, Collin, Denton, Fort Bend, Hidalgo, Montgomery, Williamson)

### Florida Sources to Audit  
- [ ] **Civil**: Fla. R. Civ. P. 1.010-1.600 + Ch. 95 Fla. Stat. + Admin Order 2022-08
- [ ] **Family**: Fla. Fam. L.R.P. 12.010-12.610 + Ch. 61 + Ch. 742
- [ ] **Criminal**: Fla. R. Crim. P. 3.010-3.850 + Rule 3.191

### Methodology
1. **Automated Scanning**: Use regex `\d+\s+(day|days|month|months|year|hours|minutes)` on each PDF
2. **Manual Review**: Sample 10% of matches to verify they're calendar deadlines
3. **Categorization**: Sort into practice areas and deadline types
4. **Baseline Count**: Create expected totals per source

## Phase 2: Coverage Matrix Creation (1 week)

```python
# scripts/coverage_audit/coverage_matrix.py
def generate_coverage_matrix():
    expected_deadlines = load_canonical_checklist()
    extracted_rules = load_all_production_rules()
    
    matrix = []
    for source in expected_deadlines:
        total_expected = len(expected_deadlines[source])
        extracted_count = count_matching_rules(source, extracted_rules)
        gap_percentage = ((total_expected - extracted_count) / total_expected) * 100
        
        matrix.append({
            'source': source,
            'expected': total_expected,
            'extracted': extracted_count,
            'gap_percent': gap_percentage,
            'status': 'FAIL' if gap_percentage > 10 else 'PASS'
        })
    
    return matrix
```

## Phase 3: Automated CI Integration (1 week)

```bash
# .github/workflows/coverage-audit.yml
- name: Coverage Audit
  run: |
    python scripts/coverage_audit/audit.py \
      --expected ./coverage_audit/canonical_checklist.csv \
      --extracted ./rules/**/*_production_ready.yaml \
      --fail-threshold 0.10 \
      --output-format github-actions
```

## Phase 4: Lawyer Validation (2 weeks)

### Validation Protocol
1. **Coverage Matrix Review**: Hand matrix to 2 TX + 2 FL attorneys
2. **Gap Analysis**: "Which deadlines do you still compute manually?"
3. **Edge Case Discovery**: Local rules, statutory oddities, recent amendments
4. **Production Readiness**: Sign-off on coverage levels

## Phase 5: Live Monitoring (Ongoing)

### API Gateway Monitoring
- Track `triggerCode` requests that match 0 rules
- Alert on new codes appearing in production
- Weekly reports on coverage gaps discovered in live usage

## Success Criteria

- [ ] **≥90% coverage** for all core sources (TX Criminal, Civil, Family + FL Civil, Criminal, Family)
- [ ] **≤5% false positives** in extracted rules (non-calendar deadlines)
- [ ] **Lawyer sign-off** on production readiness
- [ ] **Automated monitoring** detecting new gaps within 48 hours

## Timeline: 6-8 weeks total

This audit is **essential** because:
1. You're launching a production legal system where missed deadlines = malpractice
2. Your current quality controls check accuracy but not completeness
3. You need confidence metrics for client-facing deployment
4. The audit will likely discover 20-30% more deadlines in existing sources
