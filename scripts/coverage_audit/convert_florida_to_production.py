#!/usr/bin/env python3
"""
Convert Enhanced Florida Extractions to Production Format

This script converts the enhanced Florida extraction files into the proper
format expected by the rule loader system.

Usage:
    python scripts/coverage_audit/convert_florida_to_production.py
"""

import yaml
import json
import os
import re
from datetime import datetime
from typing import Dict, List, Any

def parse_enhanced_extraction(file_path: str) -> Dict:
    """Parse enhanced extraction file and extract deadline rules"""
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return {}
    
    try:
        with open(file_path, 'r') as f:
            content = f.read()
        
        # The enhanced extractions may contain multiple YAML documents
        # Split by common separators and try to parse each part
        yaml_parts = []
        
        # Try to split by common YAML document separators
        if '---' in content:
            parts = content.split('---')
        elif 'jurisdiction: Florida' in content:
            # Split by jurisdiction markers
            parts = content.split('jurisdiction: Florida')
            parts = ['jurisdiction: Florida' + part for part in parts[1:]]
        else:
            parts = [content]
        
        all_rules = []
        total_extracted = 0
        
        for part in parts:
            part = part.strip()
            if not part or len(part) < 50:
                continue
                
            try:
                data = yaml.safe_load(part)
                if isinstance(data, dict) and 'deadline_categories' in data:
                    # Extract rules from this part
                    for category_name, category_data in data['deadline_categories'].items():
                        if 'rules' in category_data:
                            for rule_id, rule_data in category_data['rules'].items():
                                all_rules.append({
                                    'rule_id': rule_id,
                                    'category': category_name,
                                    'rule_data': rule_data
                                })
                    
                    # Count total extracted
                    if 'total_deadlines_extracted' in data:
                        total_extracted += data['total_deadlines_extracted']
                        
            except yaml.YAMLError as e:
                print(f"⚠️  Could not parse YAML part: {e}")
                continue
        
        return {
            'rules': all_rules,
            'total_extracted': total_extracted or len(all_rules)
        }
        
    except Exception as e:
        print(f"❌ Error parsing {file_path}: {e}")
        return {}

def convert_to_rule_loader_format(practice_area: str, parsed_data: Dict) -> Dict:
    """Convert parsed data to rule loader format"""
    
    # Map practice areas to proper names
    practice_area_map = {
        'civil': 'Personal Injury',
        'criminal': 'Criminal Defense', 
        'family': 'Family Law'
    }
    
    # Create triggers from the extracted rules
    triggers = []
    trigger_map = {}
    
    for rule in parsed_data.get('rules', []):
        rule_data = rule['rule_data']
        
        # Extract trigger information
        trigger_text = rule_data.get('trigger', '')
        deadline_text = rule_data.get('deadline', '')
        description = rule_data.get('description', '')
        
        # Try to map to standard trigger codes
        trigger_code = map_to_trigger_code(trigger_text, description)
        
        if trigger_code and deadline_text:
            # Parse deadline offset
            offset_info = parse_deadline_offset(deadline_text)
            
            if offset_info:
                if trigger_code not in trigger_map:
                    trigger_map[trigger_code] = {
                        'code': trigger_code,
                        'description': get_trigger_description(trigger_code),
                        'deadlines': []
                    }
                
                # Create deadline entry
                deadline_entry = {
                    'code': generate_deadline_code(description),
                    'offset': offset_info['offset'],
                    'offsetUnit': offset_info['unit'],
                    'citation': rule_data.get('rule', 'Florida Rules'),
                    'description': description[:100] + '...' if len(description) > 100 else description
                }
                
                trigger_map[trigger_code]['deadlines'].append(deadline_entry)
    
    # Convert trigger map to list
    triggers = list(trigger_map.values())
    
    # Create the final rule loader format
    return {
        'meta': {
            'jurisdiction': 'FL_STATE',
            'practice_area': practice_area_map.get(practice_area, practice_area.title()),
            'version': f'enhanced-{datetime.now().strftime("%Y-%m-%d")}',
            'source': f'Florida Rules of {practice_area.title()} Procedure - Enhanced Extraction',
            'total_rules_extracted': parsed_data.get('total_extracted', 0),
            'extraction_method': 'Gemini 2.0 Flash Enhanced Analysis'
        },
        'triggers': triggers
    }

def map_to_trigger_code(trigger_text: str, description: str) -> str:
    """Map trigger text to standard trigger codes"""
    
    trigger_text_lower = trigger_text.lower()
    description_lower = description.lower()
    
    # Common trigger mappings
    if any(term in trigger_text_lower for term in ['service', 'served', 'process']):
        return 'SERVICE_OF_PROCESS'
    elif any(term in trigger_text_lower for term in ['filing', 'filed', 'petition']):
        return 'PETITION_FILED'
    elif any(term in trigger_text_lower for term in ['motion', 'request']):
        return 'MOTION_FILED'
    elif any(term in trigger_text_lower for term in ['discovery', 'interrogator', 'deposition']):
        return 'DISCOVERY_REQUEST'
    elif any(term in trigger_text_lower for term in ['trial', 'hearing']):
        return 'TRIAL_SCHEDULED'
    elif any(term in trigger_text_lower for term in ['judgment', 'verdict', 'order']):
        return 'JUDGMENT_ENTERED'
    elif any(term in trigger_text_lower for term in ['appeal', 'appellate']):
        return 'APPEAL_DEADLINE'
    elif any(term in description_lower for term in ['answer', 'response']):
        return 'SERVICE_OF_PROCESS'  # Most answers are triggered by service
    elif any(term in description_lower for term in ['motion', 'request']):
        return 'MOTION_FILED'
    else:
        # Generate a generic trigger code
        words = re.findall(r'\w+', trigger_text_lower)
        if words:
            return '_'.join(words[:3]).upper()
        return 'GENERAL_DEADLINE'

def parse_deadline_offset(deadline_text: str) -> Dict:
    """Parse deadline text to extract offset and unit"""
    
    # Common patterns for deadlines
    patterns = [
        r'(\d+)\s+(day|days)',
        r'(\d+)\s+(month|months)', 
        r'(\d+)\s+(year|years)',
        r'(\d+)\s+(hour|hours)',
        r'within\s+(\d+)\s+(day|days|month|months|year|years)',
        r'(\d+)(?:st|nd|rd|th)?\s+day',
    ]
    
    deadline_lower = deadline_text.lower()
    
    for pattern in patterns:
        match = re.search(pattern, deadline_lower)
        if match:
            offset = int(match.group(1))
            unit_text = match.group(2) if len(match.groups()) > 1 else 'day'
            
            # Normalize unit
            if unit_text.startswith('day'):
                unit = 'calendar'
            elif unit_text.startswith('month'):
                unit = 'calendar'
                offset *= 30  # Convert months to days
            elif unit_text.startswith('year'):
                unit = 'calendar'
                offset *= 365  # Convert years to days
            elif unit_text.startswith('hour'):
                unit = 'calendar'
                offset = max(1, offset // 24)  # Convert hours to days
            else:
                unit = 'calendar'
            
            return {'offset': offset, 'unit': unit}
    
    # Default fallback
    return {'offset': 30, 'unit': 'calendar'}

def get_trigger_description(trigger_code: str) -> str:
    """Get human-readable description for trigger code"""
    
    descriptions = {
        'SERVICE_OF_PROCESS': 'Date defendant is served with process',
        'PETITION_FILED': 'Date petition or complaint is filed',
        'MOTION_FILED': 'Date motion is filed with court',
        'DISCOVERY_REQUEST': 'Date discovery request is served',
        'TRIAL_SCHEDULED': 'Date trial or hearing is scheduled',
        'JUDGMENT_ENTERED': 'Date judgment or order is entered',
        'APPEAL_DEADLINE': 'Date triggering appeal deadline',
        'GENERAL_DEADLINE': 'General deadline trigger'
    }
    
    return descriptions.get(trigger_code, f'Trigger for {trigger_code.replace("_", " ").lower()}')

def generate_deadline_code(description: str) -> str:
    """Generate deadline code from description"""
    
    # Extract key words and create code
    words = re.findall(r'\w+', description.lower())
    key_words = [w for w in words if w not in ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by']]
    
    if key_words:
        return '_'.join(key_words[:3]).upper()
    else:
        return 'DEADLINE'

def main():
    """Main conversion function"""
    
    print("🔄 Converting Enhanced Florida Extractions to Production Format")
    print("=" * 70)
    
    # Define source and target files
    conversions = [
        {
            'practice_area': 'civil',
            'source': 'rules/florida/processed/civil_deadlines_enhanced_extraction.yaml',
            'target': 'rules/FL_STATE_personal_injury.yaml'
        },
        {
            'practice_area': 'criminal', 
            'source': 'rules/florida/processed/criminal_deadlines_enhanced_extraction.yaml',
            'target': 'rules/FL_STATE_criminal_defense.yaml'
        },
        {
            'practice_area': 'family',
            'source': 'rules/florida/processed/family_deadlines_enhanced_extraction.yaml', 
            'target': 'rules/FL_STATE_family_law.yaml'
        }
    ]
    
    total_converted = 0
    
    for conversion in conversions:
        practice_area = conversion['practice_area']
        source_file = conversion['source']
        target_file = conversion['target']
        
        print(f"\n📊 Converting {practice_area.upper()}...")
        print(f"Source: {source_file}")
        print(f"Target: {target_file}")
        
        # Parse enhanced extraction
        parsed_data = parse_enhanced_extraction(source_file)
        
        if not parsed_data:
            print(f"❌ Failed to parse {source_file}")
            continue
        
        print(f"✅ Parsed {parsed_data.get('total_extracted', 0)} rules")
        
        # Convert to rule loader format
        rule_data = convert_to_rule_loader_format(practice_area, parsed_data)
        
        print(f"✅ Generated {len(rule_data['triggers'])} triggers")
        
        # Save to target file
        try:
            with open(target_file, 'w') as f:
                yaml.dump(rule_data, f, default_flow_style=False, sort_keys=False)
            
            print(f"✅ Saved to {target_file}")
            total_converted += 1
            
        except Exception as e:
            print(f"❌ Error saving {target_file}: {e}")
    
    print(f"\n🎯 CONVERSION COMPLETE")
    print(f"Successfully converted {total_converted}/3 practice areas")
    
    if total_converted == 3:
        print(f"\n🚀 NEXT STEPS:")
        print(f"1. Run tests: npm test")
        print(f"2. Verify Florida rules are loading correctly")
        print(f"3. Check that SERVICE_OF_PROCESS trigger is available")
        print(f"4. Update production deployment")
    else:
        print(f"\n⚠️  Some conversions failed. Check error messages above.")

if __name__ == "__main__":
    main()
