#!/usr/bin/env python3
"""
Florida Extraction Gap Analysis

This script investigates why Florida extraction coverage is low (40-52%)
compared to the PDF scan baseline and Texas extraction success.

Usage:
    python scripts/coverage_audit/florida_gap_analysis.py
"""

import json
import yaml
import os
import re
from collections import defaultdict, Counter
from typing import Dict, List, Tuple

def load_canonical_checklist():
    """Load PDF scan results"""
    with open("scripts/coverage_audit/canonical_checklist.json", 'r') as f:
        return json.load(f)

def load_florida_extractions():
    """Load all Florida extraction files"""
    extractions = {}
    
    files = [
        ("civil", "rules/florida/processed/civil_deadlines_gemini_2_0_flash_production_ready.yaml"),
        ("criminal", "rules/florida/processed/criminal_deadlines_gemini_production_ready.yaml"),
        ("family", "rules/florida/processed/family_deadlines_gemini_production_ready.yaml")
    ]
    
    for practice_area, file_path in files:
        if os.path.exists(file_path):
            with open(file_path, 'r') as f:
                extractions[practice_area] = yaml.safe_load(f)
        else:
            print(f"⚠️  File not found: {file_path}")
    
    return extractions

def analyze_pdf_vs_extraction_gap(practice_area: str, checklist: Dict, extractions: Dict):
    """Analyze the gap between PDF scan and extraction for a practice area"""
    
    # Get PDF scan data
    scan_key = f"florida_{practice_area}"
    scan_data = checklist.get('sources', {}).get(scan_key, {})
    pdf_deadlines = scan_data.get('scan_stats', {}).get('calendar_deadlines', 0)
    pdf_sample_matches = scan_data.get('sample_matches', [])
    
    # Get extraction data
    extraction_data = extractions.get(practice_area, {})
    extracted_count = extraction_data.get('total_deadlines_extracted', 0)
    
    # Calculate gap
    gap_count = pdf_deadlines - extracted_count
    gap_percentage = (gap_count / pdf_deadlines * 100) if pdf_deadlines > 0 else 0
    
    print(f"\n📊 {practice_area.upper()} GAP ANALYSIS")
    print(f"PDF Scan Baseline: {pdf_deadlines} deadlines")
    print(f"Extracted: {extracted_count} deadlines")
    print(f"Gap: {gap_count} missing deadlines ({gap_percentage:.1f}%)")
    
    # Analyze PDF sample matches to see what types of deadlines were found
    print(f"\n🔍 PDF SCAN SAMPLE PATTERNS:")
    deadline_patterns = []
    for match in pdf_sample_matches[:10]:  # First 10 samples
        text = match.get('text', '')
        context = match.get('context', '')[:100] + "..."
        confidence = match.get('confidence', 0)
        page = match.get('page', 0)
        
        deadline_patterns.append(text)
        print(f"  • '{text}' (confidence: {confidence:.1f}, page: {page})")
        print(f"    Context: {context}")
    
    # Analyze extraction patterns
    print(f"\n🔍 EXTRACTION SAMPLE PATTERNS:")
    if 'deadline_categories' in extraction_data:
        rule_count = 0
        for category_name, category_data in extraction_data['deadline_categories'].items():
            if rule_count >= 10:  # Limit to first 10 rules
                break
            print(f"  Category: {category_name}")
            for rule_key, rule_data in category_data.get('rules', {}).items():
                if rule_count >= 10:
                    break
                deadline = rule_data.get('deadline', '')
                description = rule_data.get('description', '')[:80] + "..."
                print(f"    • '{deadline}' - {description}")
                rule_count += 1
    
    return {
        'practice_area': practice_area,
        'pdf_deadlines': pdf_deadlines,
        'extracted_count': extracted_count,
        'gap_count': gap_count,
        'gap_percentage': gap_percentage,
        'pdf_patterns': deadline_patterns
    }

def compare_texas_vs_florida_success():
    """Compare Texas vs Florida extraction patterns to identify differences"""
    
    print(f"\n🔄 TEXAS vs FLORIDA COMPARISON")
    
    # Texas success rates (from our previous analysis)
    texas_rates = {
        'criminal': 116.4,  # 397/341
        'civil': 104.9,     # 257/245 (TRCP only)
        'family': 122.7     # 529/431
    }
    
    # Florida rates (calculated)
    florida_rates = {
        'civil': 40.0,      # 130/325
        'criminal': 52.5,   # 167/318
        'family': 43.6      # 92/211
    }
    
    print(f"Texas Success Rates:")
    for area, rate in texas_rates.items():
        print(f"  {area.title()}: {rate:.1f}%")
    
    print(f"\nFlorida Success Rates:")
    for area, rate in florida_rates.items():
        print(f"  {area.title()}: {rate:.1f}%")
    
    print(f"\nGap Analysis:")
    for area in ['criminal', 'family']:  # Common areas
        if area in texas_rates and area in florida_rates:
            gap = texas_rates[area] - florida_rates[area]
            print(f"  {area.title()}: {gap:.1f}% gap (Texas better)")

def analyze_pdf_quality():
    """Analyze PDF quality and text extraction issues"""
    
    print(f"\n📄 PDF QUALITY ANALYSIS")
    
    checklist = load_canonical_checklist()
    
    for practice_area in ['civil', 'criminal', 'family']:
        scan_key = f"florida_{practice_area}"
        scan_data = checklist.get('sources', {}).get(scan_key, {})
        
        total_pages = scan_data.get('scan_stats', {}).get('total_pages', 0)
        total_matches = scan_data.get('scan_stats', {}).get('total_matches', 0)
        calendar_deadlines = scan_data.get('scan_stats', {}).get('calendar_deadlines', 0)
        
        if total_pages > 0:
            matches_per_page = total_matches / total_pages
            deadlines_per_page = calendar_deadlines / total_pages
            
            print(f"\n{practice_area.title()}:")
            print(f"  Pages: {total_pages}")
            print(f"  Total matches: {total_matches} ({matches_per_page:.1f} per page)")
            print(f"  Calendar deadlines: {calendar_deadlines} ({deadlines_per_page:.1f} per page)")
            
            # Check for potential text extraction issues
            sample_matches = scan_data.get('sample_matches', [])
            if sample_matches:
                # Look for signs of poor text extraction
                poor_extraction_indicators = 0
                for match in sample_matches[:5]:
                    context = match.get('context', '')
                    if any(indicator in context for indicator in ['\n', '  ', 'Florida Rules', 'May  22, 202 5']):
                        poor_extraction_indicators += 1
                
                if poor_extraction_indicators >= 3:
                    print(f"  ⚠️  Potential text extraction issues detected")

def generate_recommendations():
    """Generate specific recommendations for improving Florida extraction"""
    
    print(f"\n💡 RECOMMENDATIONS FOR FLORIDA IMPROVEMENT")
    
    print(f"\n1. **Immediate Actions:**")
    print(f"   - Re-run Gemini extraction with enhanced prompts")
    print(f"   - Increase extraction comprehensiveness")
    print(f"   - Focus on discovery and motion deadlines")
    
    print(f"\n2. **PDF Quality Issues:**")
    print(f"   - Check for text extraction problems in Florida PDFs")
    print(f"   - Consider OCR preprocessing if needed")
    print(f"   - Verify PDF text is machine-readable")
    
    print(f"\n3. **Extraction Optimization:**")
    print(f"   - Use Texas extraction prompts as baseline")
    print(f"   - Add Florida-specific legal terminology")
    print(f"   - Increase context window for complex rules")
    
    print(f"\n4. **Validation Steps:**")
    print(f"   - Manual spot-check of missed deadlines")
    print(f"   - Compare against Florida attorney knowledge")
    print(f"   - Cross-reference with legal databases")

def main():
    """Main analysis function"""
    
    print("🔍 FLORIDA EXTRACTION GAP ANALYSIS")
    print("=" * 50)
    
    # Load data
    checklist = load_canonical_checklist()
    extractions = load_florida_extractions()
    
    # Analyze each practice area
    gap_results = []
    for practice_area in ['civil', 'criminal', 'family']:
        result = analyze_pdf_vs_extraction_gap(practice_area, checklist, extractions)
        gap_results.append(result)
    
    # Compare with Texas success
    compare_texas_vs_florida_success()
    
    # Analyze PDF quality
    analyze_pdf_quality()
    
    # Generate recommendations
    generate_recommendations()
    
    # Summary
    print(f"\n📋 SUMMARY")
    print(f"=" * 30)
    
    total_pdf_deadlines = sum(r['pdf_deadlines'] for r in gap_results)
    total_extracted = sum(r['extracted_count'] for r in gap_results)
    total_gap = total_pdf_deadlines - total_extracted
    overall_coverage = (total_extracted / total_pdf_deadlines * 100) if total_pdf_deadlines > 0 else 0
    
    print(f"Overall Florida Coverage: {overall_coverage:.1f}%")
    print(f"Total Missing Deadlines: {total_gap}")
    print(f"Target for 90% Coverage: {int(total_pdf_deadlines * 0.9)} deadlines")
    print(f"Additional Extractions Needed: {int(total_pdf_deadlines * 0.9) - total_extracted}")
    
    print(f"\n🎯 NEXT STEPS:")
    print(f"1. Re-extract Florida rules with enhanced prompts")
    print(f"2. Target {total_gap} additional deadline extractions")
    print(f"3. Focus on discovery, motion, and trial deadlines")
    print(f"4. Validate against attorney knowledge")

if __name__ == "__main__":
    main()
