#!/usr/bin/env python3
"""
Production Readiness Assessment

Simple script to assess production readiness based on coverage audit results
and identify missing sources that need to be acquired.

Usage:
    python scripts/coverage_audit/production_readiness.py
"""

import json
import yaml
import os
from datetime import datetime

def load_canonical_checklist():
    """Load PDF scan results"""
    checklist_path = "scripts/coverage_audit/canonical_checklist.json"
    if os.path.exists(checklist_path):
        with open(checklist_path, 'r') as f:
            return json.load(f)
    return {}

def load_canonical_sources():
    """Load canonical sources configuration"""
    config_path = "scripts/coverage_audit/canonical_sources.yaml"
    with open(config_path, 'r') as f:
        return yaml.safe_load(f)

def assess_coverage(jurisdiction, practice_area, checklist, sources_config):
    """Assess coverage for a jurisdiction/practice area"""

    # Get PDF scan baseline - sum all sources for this jurisdiction/practice area
    expected_deadlines = 0
    for source_key, source_data in checklist.get('sources', {}).items():
        if source_key.startswith(f"{jurisdiction}_{practice_area}"):
            expected_deadlines += source_data.get('scan_stats', {}).get('calendar_deadlines', 0)
    
    # Get current extraction count (from actual production-ready files)
    extraction_counts = {
        ('texas', 'criminal'): 397,
        ('texas', 'civil'): 257,
        ('texas', 'family'): 529,
        ('florida', 'civil'): 130,    # From civil_deadlines_gemini_2_0_flash_production_ready.yaml
        ('florida', 'criminal'): 167, # From criminal_deadlines_gemini_production_ready.yaml
        ('florida', 'family'): 92,    # From family_deadlines_gemini_production_ready.yaml
    }
    
    extracted_count = extraction_counts.get((jurisdiction, practice_area), 0)
    
    # Calculate coverage percentage
    if expected_deadlines > 0:
        coverage_percentage = (extracted_count / expected_deadlines) * 100
    else:
        coverage_percentage = 0
    
    # Get missing sources
    jurisdiction_config = sources_config.get(jurisdiction, {})
    area_config = jurisdiction_config.get(practice_area, {})
    missing_sources = []
    critical_missing = []
    
    for source in area_config.get('sources', []):
        if source.get('status') == 'missing':
            missing_sources.append(source['name'])
            if source.get('priority') == 'critical':
                critical_missing.append(source['name'])
    
    # Determine production readiness
    production_ready = (
        coverage_percentage >= 90 and  # 90%+ coverage of scanned sources
        len(critical_missing) == 0     # No critical missing sources
    )
    
    return {
        'jurisdiction': jurisdiction,
        'practice_area': practice_area,
        'expected_deadlines': expected_deadlines,
        'extracted_count': extracted_count,
        'coverage_percentage': coverage_percentage,
        'missing_sources': missing_sources,
        'critical_missing': critical_missing,
        'production_ready': production_ready
    }

def generate_report():
    """Generate production readiness report"""
    
    checklist = load_canonical_checklist()
    sources_config = load_canonical_sources()
    
    jurisdictions = ['texas', 'florida']
    practice_areas = ['criminal', 'civil', 'family']
    
    report = []
    report.append("# Legal Rules Production Readiness Assessment")
    report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append("")
    
    all_assessments = []
    
    for jurisdiction in jurisdictions:
        for practice_area in practice_areas:
            assessment = assess_coverage(jurisdiction, practice_area, checklist, sources_config)
            all_assessments.append(assessment)
    
    # Executive Summary
    ready_count = len([a for a in all_assessments if a['production_ready']])
    total_count = len(all_assessments)
    
    report.append("## Executive Summary")
    report.append(f"- **Production Ready**: {ready_count}/{total_count} ({ready_count/total_count*100:.1f}%)")
    
    # Calculate averages for areas with data
    areas_with_data = [a for a in all_assessments if a['expected_deadlines'] > 0]
    if areas_with_data:
        avg_coverage = sum(a['coverage_percentage'] for a in areas_with_data) / len(areas_with_data)
        report.append(f"- **Average Coverage** (scanned sources): {avg_coverage:.1f}%")
    
    report.append("")
    
    # Detailed Results
    for jurisdiction in jurisdictions:
        jurisdiction_assessments = [a for a in all_assessments if a['jurisdiction'] == jurisdiction]
        
        report.append(f"## {jurisdiction.upper()}")
        report.append("")
        report.append("| Practice Area | PDF Baseline | Extracted | Coverage | Missing Sources | Status |")
        report.append("|---------------|--------------|-----------|----------|-----------------|--------|")
        
        for assessment in jurisdiction_assessments:
            status_emoji = "✅" if assessment['production_ready'] else "❌"
            missing_count = len(assessment['missing_sources'])
            missing_text = f"{missing_count} sources" if missing_count > 0 else "None"
            
            if assessment['expected_deadlines'] > 0:
                baseline_text = str(assessment['expected_deadlines'])
                coverage_text = f"{assessment['coverage_percentage']:.1f}%"
            else:
                baseline_text = "Not scanned"
                coverage_text = "N/A"
            
            report.append(f"| {assessment['practice_area'].title()} | "
                        f"{baseline_text} | "
                        f"{assessment['extracted_count']} | "
                        f"{coverage_text} | "
                        f"{missing_text} | "
                        f"{status_emoji} {'READY' if assessment['production_ready'] else 'NOT READY'} |")
        
        report.append("")
        
        # Missing Sources Details
        all_missing = []
        all_critical = []
        for assessment in jurisdiction_assessments:
            all_missing.extend(assessment['missing_sources'])
            all_critical.extend(assessment['critical_missing'])
        
        if all_critical:
            report.append(f"### 🚨 Critical Missing Sources for {jurisdiction.upper()}")
            for source in set(all_critical):
                report.append(f"- {source}")
            report.append("")
        
        if all_missing:
            report.append(f"### Missing Sources for {jurisdiction.upper()}")
            for source in set(all_missing):
                if source not in all_critical:  # Don't duplicate critical ones
                    report.append(f"- {source}")
            report.append("")
    
    # Key Findings
    report.append("## Key Findings")
    report.append("")
    
    # Texas findings
    texas_assessments = [a for a in all_assessments if a['jurisdiction'] == 'texas']
    texas_ready = [a for a in texas_assessments if a['production_ready']]
    
    report.append(f"### Texas ({len(texas_ready)}/3 areas ready)")
    for assessment in texas_assessments:
        if assessment['expected_deadlines'] > 0:
            status = "✅ READY" if assessment['production_ready'] else "⚠️ NEEDS WORK"
            report.append(f"- **{assessment['practice_area'].title()}**: {status} "
                        f"({assessment['coverage_percentage']:.1f}% coverage)")
    report.append("")
    
    # Florida findings
    florida_assessments = [a for a in all_assessments if a['jurisdiction'] == 'florida']
    florida_ready = [a for a in florida_assessments if a['production_ready']]

    report.append(f"### Florida ({len(florida_ready)}/3 areas ready)")
    for assessment in florida_assessments:
        if assessment['expected_deadlines'] > 0:
            status = "✅ READY" if assessment['production_ready'] else "⚠️ NEEDS WORK"
            report.append(f"- **{assessment['practice_area'].title()}**: {status} "
                        f"({assessment['coverage_percentage']:.1f}% coverage)")
    report.append("")
    
    # Next Steps
    report.append("## Immediate Next Steps")
    report.append("")
    report.append("### Phase 1: Complete Texas Coverage (1-2 weeks)")
    report.append("1. **Acquire missing Texas sources:**")
    
    texas_missing = []
    for assessment in texas_assessments:
        texas_missing.extend(assessment['missing_sources'])
    
    for source in set(texas_missing):
        report.append(f"   - {source}")
    
    report.append("2. **Scan new sources with PDF scanner**")
    report.append("3. **Extract rules using Gemini 2.0 Flash**")
    report.append("4. **Re-run coverage audit**")
    report.append("")
    
    report.append("### Phase 2: Florida Implementation (2-3 weeks)")
    report.append("1. **Acquire Florida legal sources:**")
    report.append("   - Florida Rules of Civil Procedure")
    report.append("   - Florida Rules of Criminal Procedure")
    report.append("   - Florida Family Law Rules")
    report.append("   - Florida Statutes (relevant chapters)")
    report.append("2. **Run PDF scanner on Florida sources**")
    report.append("3. **Extract rules using Gemini 2.0 Flash**")
    report.append("4. **Establish Florida coverage baselines**")
    report.append("")
    
    report.append("### Phase 3: Production Deployment (1 week)")
    report.append("1. **Lawyer validation** of ready areas")
    report.append("2. **CI/CD integration** of coverage audit")
    report.append("3. **Staged deployment** (ready areas first)")
    report.append("4. **Live monitoring** setup")
    
    return "\n".join(report)

def main():
    """Main function"""
    print("🔍 Generating Production Readiness Assessment...")
    
    report = generate_report()
    
    # Save report
    output_path = "scripts/coverage_audit/production_readiness_report.md"
    with open(output_path, 'w') as f:
        f.write(report)
    
    print(f"📄 Report saved to {output_path}")
    print("\n" + "="*60)
    print(report)

if __name__ == "__main__":
    main()
