# Legal Rules Coverage Audit System

## Overview

The Coverage Audit System ensures comprehensive coverage of all deadline-bearing provisions from authoritative legal sources. This system is **essential** for production deployment to prevent missed legal deadlines that could result in malpractice claims.

## 🎯 Current Status

### Texas (2/3 areas production ready)
- ✅ **Criminal**: 397 rules extracted vs 341 expected (116.4% coverage) - **PRODUCTION READY**
- ⚠️ **Civil**: 257 rules extracted vs 198 expected (129.8% coverage) - Missing critical TRCP source
- ✅ **Family**: 529 rules extracted vs 431 expected (122.7% coverage) - **PRODUCTION READY**

### Florida (Needs implementation)
- ❌ **All areas**: No PDF baselines established yet

## 📁 System Components

### Core Scripts

1. **`pdf_scanner.py`** - Scans PDF legal documents to establish baseline deadline counts
   ```bash
   python scripts/coverage_audit/pdf_scanner.py --scan-all
   ```

2. **`audit.py`** - Compares extracted rules against PDF baselines
   ```bash
   python scripts/coverage_audit/audit.py --mode full
   ```

3. **`production_readiness.py`** - Assesses production readiness
   ```bash
   python scripts/coverage_audit/production_readiness.py
   ```

4. **`acquire_sources.py`** - Helps identify and acquire missing sources
   ```bash
   python scripts/coverage_audit/acquire_sources.py --list-missing
   ```

### Configuration Files

- **`canonical_sources.yaml`** - Defines authoritative sources and coverage thresholds
- **`canonical_checklist.json`** - PDF scan results with baseline deadline counts

### CI/CD Integration

- **`.github/workflows/coverage-audit.yml`** - Automated coverage auditing in CI/CD
- Runs on every push to rules files
- Blocks deployment if critical coverage failures detected
- Generates coverage badges and reports

## 🚀 Quick Start

### 1. Run Current Coverage Audit
```bash
# Check current coverage status
python scripts/coverage_audit/audit.py --mode full

# Generate production readiness report
python scripts/coverage_audit/production_readiness.py
```

### 2. Identify Missing Sources
```bash
# List all missing sources
python scripts/coverage_audit/acquire_sources.py --list-missing

# Generate acquisition plan
python scripts/coverage_audit/acquire_sources.py --generate-plan
```

### 3. Scan New PDF Sources
```bash
# Scan a new PDF source
python scripts/coverage_audit/pdf_scanner.py \
  --pdf rules/Texas/sources/new_source.pdf \
  --jurisdiction texas \
  --practice-area civil
```

## 📊 Key Metrics

### Coverage Thresholds
- **Critical sources**: ≥90% coverage required
- **High priority**: ≥80% coverage required  
- **Medium priority**: ≥70% coverage acceptable

### Quality Thresholds
- **False positive rate**: ≤5%
- **Vague trigger rate**: ≤15%
- **Duplicate rate**: ≤10%

## 🔧 Implementation Phases

### ✅ Phase 1: PDF Baseline Establishment (COMPLETED)
- [x] Created PDF scanner with regex pattern matching
- [x] Scanned Texas sources (Criminal, Civil, Family)
- [x] Established baseline of 970 high-confidence deadlines
- [x] Generated canonical checklist

### ✅ Phase 2: Coverage Audit Framework (COMPLETED)
- [x] Built coverage comparison system
- [x] Created production readiness assessment
- [x] Integrated with existing quality controls
- [x] Generated comprehensive reports

### ✅ Phase 3: CI/CD Integration (COMPLETED)
- [x] Created GitHub Actions workflow
- [x] Added automated coverage checking
- [x] Implemented critical failure blocking
- [x] Added coverage badge generation

### 🔄 Phase 4: Missing Source Acquisition (IN PROGRESS)
- [x] Identified 3 missing Texas sources
- [x] Created acquisition helper tools
- [ ] **CRITICAL**: Acquire Texas Rules of Civil Procedure (TRCP)
- [ ] Acquire local county rules
- [ ] Process new sources through pipeline

### 📅 Phase 5: Florida Implementation (PLANNED)
- [ ] Acquire Florida legal sources
- [ ] Run PDF scanner on Florida sources  
- [ ] Extract rules using Gemini 2.0 Flash
- [ ] Establish Florida coverage baselines

### 🎯 Phase 6: Production Deployment (PLANNED)
- [ ] Lawyer validation of ready areas
- [ ] Staged deployment (ready areas first)
- [ ] Live monitoring setup
- [ ] Coverage gap alerting

## 🚨 Critical Next Steps

### Immediate (This Week)
1. **Acquire Texas Rules of Civil Procedure (TRCP)**
   - Download from: https://www.txcourts.gov/media/1436510/trcp-2023.pdf
   - This is blocking Texas Civil from production readiness

2. **Process TRCP through pipeline**
   - Scan with PDF scanner
   - Extract with Gemini 2.0 Flash
   - Update coverage audit

### Short Term (Next 2 Weeks)
1. **Complete Texas coverage**
   - Acquire remaining missing sources
   - Achieve 90%+ coverage on all Texas areas

2. **Begin Florida implementation**
   - Acquire Florida legal sources
   - Establish PDF baselines

### Medium Term (Next Month)
1. **Florida full implementation**
2. **Lawyer validation**
3. **Production deployment**

## 📈 Success Metrics

### Production Readiness Criteria
- [ ] ≥90% coverage for all critical sources
- [ ] ≤5% false positive rate
- [ ] No critical missing sources
- [ ] Lawyer sign-off completed
- [ ] CI/CD integration passing

### Current Progress
- **Texas**: 67% production ready (2/3 areas)
- **Florida**: 0% production ready (0/3 areas)
- **Overall**: 33% production ready (2/6 areas)

## 🔍 Monitoring & Maintenance

### Automated Monitoring
- Weekly coverage audits via GitHub Actions
- Critical failure alerts
- Coverage badge updates
- Quality control integration

### Manual Reviews
- Monthly lawyer validation
- Quarterly source updates
- Annual comprehensive audit

## 📚 Documentation

- **Production Readiness Report**: `scripts/coverage_audit/production_readiness_report.md`
- **Source Acquisition Plan**: `scripts/coverage_audit/source_acquisition_plan.md`
- **Coverage Audit Results**: Generated in CI/CD artifacts

## 🆘 Troubleshooting

### Common Issues

1. **"No module named 'scripts'" error**
   - Run from repository root directory
   - Ensure Python path includes current directory

2. **PDF scanning fails**
   - Check PyPDF2 installation: `pip install PyPDF2`
   - Verify PDF file exists and is readable

3. **Coverage audit shows 0% coverage**
   - Check file paths in canonical_sources.yaml
   - Verify extracted rules files exist

4. **CI/CD workflow fails**
   - Check Python dependencies in workflow
   - Verify all required files are committed

### Getting Help

For issues with the coverage audit system:
1. Check this README
2. Review error logs in CI/CD artifacts
3. Run individual scripts with `--help` flag
4. Check canonical_sources.yaml configuration

---

**Remember**: This system is critical for legal compliance. Missing deadlines in production could result in malpractice claims. Always ensure coverage audits pass before deployment.
