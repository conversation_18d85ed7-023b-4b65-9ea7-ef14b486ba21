#!/usr/bin/env python3
"""
Florida Enhanced Extraction Results Analysis

This script analyzes the results of the enhanced Florida extractions
and compares them to the original extractions and PDF baselines.

Usage:
    python scripts/coverage_audit/florida_results_analysis.py
"""

import json
import yaml
import os
import re
from collections import defaultdict

def load_canonical_checklist():
    """Load PDF scan results"""
    with open("scripts/coverage_audit/canonical_checklist.json", 'r') as f:
        return json.load(f)

def count_deadlines_in_file(file_path):
    """Count deadlines in a YAML file by looking for total_deadlines_extracted"""
    if not os.path.exists(file_path):
        return 0
    
    try:
        with open(file_path, 'r') as f:
            content = f.read()
            
        # Find all total_deadlines_extracted values
        matches = re.findall(r'total_deadlines_extracted:\s*(\d+)', content)
        if matches:
            # Sum all the counts (for chunked extractions)
            return sum(int(match) for match in matches)
        
        # Fallback: try to parse as YAML and count rules
        try:
            data = yaml.safe_load(content)
            if isinstance(data, dict) and 'deadline_categories' in data:
                total = 0
                for category in data['deadline_categories'].values():
                    if 'rules' in category:
                        total += len(category['rules'])
                return total
        except:
            pass
            
        return 0
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        return 0

def analyze_florida_improvements():
    """Analyze improvements from enhanced extractions"""
    
    print("🔍 FLORIDA ENHANCED EXTRACTION ANALYSIS")
    print("=" * 60)
    
    # Load PDF baselines
    checklist = load_canonical_checklist()
    
    # Define file paths
    files = {
        'civil': {
            'original': 'rules/florida/processed/civil_deadlines_gemini_2_0_flash_production_ready.yaml',
            'enhanced': 'rules/florida/processed/civil_deadlines_enhanced_extraction.yaml',
            'pdf_baseline': checklist.get('sources', {}).get('florida_civil', {}).get('scan_stats', {}).get('calendar_deadlines', 0)
        },
        'criminal': {
            'original': 'rules/florida/processed/criminal_deadlines_gemini_production_ready.yaml',
            'enhanced': 'rules/florida/processed/criminal_deadlines_enhanced_extraction.yaml',
            'pdf_baseline': checklist.get('sources', {}).get('florida_criminal', {}).get('scan_stats', {}).get('calendar_deadlines', 0)
        },
        'family': {
            'original': 'rules/florida/processed/family_deadlines_gemini_production_ready.yaml',
            'enhanced': 'rules/florida/processed/family_deadlines_enhanced_extraction.yaml',
            'pdf_baseline': checklist.get('sources', {}).get('florida_family', {}).get('scan_stats', {}).get('calendar_deadlines', 0)
        }
    }
    
    # Analyze each practice area
    results = {}
    total_original = 0
    total_enhanced = 0
    total_baseline = 0
    
    for practice_area, paths in files.items():
        original_count = count_deadlines_in_file(paths['original'])
        enhanced_count = count_deadlines_in_file(paths['enhanced'])
        pdf_baseline = paths['pdf_baseline']
        
        improvement = enhanced_count - original_count
        improvement_pct = (improvement / original_count * 100) if original_count > 0 else 0
        
        original_coverage = (original_count / pdf_baseline * 100) if pdf_baseline > 0 else 0
        enhanced_coverage = (enhanced_count / pdf_baseline * 100) if pdf_baseline > 0 else 0
        coverage_improvement = enhanced_coverage - original_coverage
        
        results[practice_area] = {
            'original_count': original_count,
            'enhanced_count': enhanced_count,
            'pdf_baseline': pdf_baseline,
            'improvement': improvement,
            'improvement_pct': improvement_pct,
            'original_coverage': original_coverage,
            'enhanced_coverage': enhanced_coverage,
            'coverage_improvement': coverage_improvement,
            'production_ready': enhanced_coverage >= 90
        }
        
        total_original += original_count
        total_enhanced += enhanced_count
        total_baseline += pdf_baseline
        
        print(f"\n📊 {practice_area.upper()} RESULTS:")
        print(f"  PDF Baseline: {pdf_baseline} deadlines")
        print(f"  Original Extraction: {original_count} deadlines ({original_coverage:.1f}% coverage)")
        print(f"  Enhanced Extraction: {enhanced_count} deadlines ({enhanced_coverage:.1f}% coverage)")
        print(f"  Improvement: +{improvement} deadlines (+{improvement_pct:.1f}%)")
        print(f"  Coverage Improvement: +{coverage_improvement:.1f}%")
        print(f"  Production Ready: {'✅ YES' if results[practice_area]['production_ready'] else '❌ NO'}")
    
    # Overall summary
    overall_original_coverage = (total_original / total_baseline * 100) if total_baseline > 0 else 0
    overall_enhanced_coverage = (total_enhanced / total_baseline * 100) if total_baseline > 0 else 0
    overall_improvement = total_enhanced - total_original
    overall_coverage_improvement = overall_enhanced_coverage - overall_original_coverage
    
    print(f"\n🎯 OVERALL FLORIDA RESULTS:")
    print(f"  Total PDF Baseline: {total_baseline} deadlines")
    print(f"  Original Total: {total_original} deadlines ({overall_original_coverage:.1f}% coverage)")
    print(f"  Enhanced Total: {total_enhanced} deadlines ({overall_enhanced_coverage:.1f}% coverage)")
    print(f"  Total Improvement: +{overall_improvement} deadlines")
    print(f"  Coverage Improvement: +{overall_coverage_improvement:.1f}%")
    
    # Production readiness assessment
    ready_areas = len([r for r in results.values() if r['production_ready']])
    total_areas = len(results)
    
    print(f"\n🚀 PRODUCTION READINESS:")
    print(f"  Ready Areas: {ready_areas}/{total_areas} ({ready_areas/total_areas*100:.1f}%)")
    
    for area, result in results.items():
        status = "✅ READY" if result['production_ready'] else "❌ NOT READY"
        print(f"  {area.title()}: {status} ({result['enhanced_coverage']:.1f}% coverage)")
    
    # Success metrics
    print(f"\n📈 SUCCESS METRICS:")
    target_coverage = 90.0
    areas_meeting_target = len([r for r in results.values() if r['enhanced_coverage'] >= target_coverage])
    
    print(f"  Target Coverage: {target_coverage}%")
    print(f"  Areas Meeting Target: {areas_meeting_target}/{total_areas}")
    
    if overall_enhanced_coverage >= target_coverage:
        print(f"  🎉 OVERALL SUCCESS: {overall_enhanced_coverage:.1f}% coverage achieved!")
    else:
        gap = target_coverage - overall_enhanced_coverage
        print(f"  ⚠️  Gap Remaining: {gap:.1f}% to reach {target_coverage}% target")
    
    # Recommendations
    print(f"\n💡 RECOMMENDATIONS:")
    
    if ready_areas == total_areas:
        print(f"  🎯 All Florida areas are production ready!")
        print(f"  ✅ Deploy Florida to production")
        print(f"  ✅ Update coverage audit system")
        print(f"  ✅ Run final validation with attorneys")
    else:
        not_ready = [area for area, result in results.items() if not result['production_ready']]
        print(f"  🔧 Areas needing improvement: {', '.join(not_ready)}")
        
        for area in not_ready:
            result = results[area]
            gap = 90 - result['enhanced_coverage']
            additional_needed = int(result['pdf_baseline'] * gap / 100)
            print(f"    {area.title()}: Need +{additional_needed} more deadlines ({gap:.1f}% gap)")
    
    return results

def generate_comparison_report(results):
    """Generate a detailed comparison report"""
    
    report = []
    report.append("# Florida Enhanced Extraction Results Report")
    report.append(f"Generated: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append("")
    
    # Executive Summary
    ready_count = len([r for r in results.values() if r['production_ready']])
    total_count = len(results)
    
    report.append("## Executive Summary")
    report.append(f"- **Enhanced Extraction Success**: Significant improvements across all Florida practice areas")
    report.append(f"- **Production Ready Areas**: {ready_count}/{total_count} ({ready_count/total_count*100:.1f}%)")
    
    total_improvement = sum(r['improvement'] for r in results.values())
    avg_coverage_improvement = sum(r['coverage_improvement'] for r in results.values()) / len(results)
    
    report.append(f"- **Total Additional Deadlines**: +{total_improvement}")
    report.append(f"- **Average Coverage Improvement**: +{avg_coverage_improvement:.1f}%")
    report.append("")
    
    # Detailed Results
    report.append("## Detailed Results by Practice Area")
    report.append("")
    report.append("| Practice Area | Original | Enhanced | Improvement | Coverage | Status |")
    report.append("|---------------|----------|----------|-------------|----------|--------|")
    
    for area, result in results.items():
        status = "✅ READY" if result['production_ready'] else "❌ NOT READY"
        report.append(f"| {area.title()} | {result['original_count']} | "
                     f"{result['enhanced_count']} | +{result['improvement']} | "
                     f"{result['enhanced_coverage']:.1f}% | {status} |")
    
    report.append("")
    
    # Impact Analysis
    report.append("## Impact Analysis")
    report.append("")
    
    for area, result in results.items():
        report.append(f"### {area.title()}")
        report.append(f"- **Baseline**: {result['pdf_baseline']} deadlines from PDF scan")
        report.append(f"- **Original**: {result['original_count']} deadlines ({result['original_coverage']:.1f}% coverage)")
        report.append(f"- **Enhanced**: {result['enhanced_count']} deadlines ({result['enhanced_coverage']:.1f}% coverage)")
        report.append(f"- **Improvement**: +{result['improvement']} deadlines (+{result['improvement_pct']:.1f}%)")
        report.append(f"- **Production Ready**: {'Yes' if result['production_ready'] else 'No'}")
        report.append("")
    
    return "\n".join(report)

def main():
    """Main analysis function"""
    
    # Run analysis
    results = analyze_florida_improvements()
    
    # Generate detailed report
    report = generate_comparison_report(results)
    
    # Save report
    output_path = "scripts/coverage_audit/florida_enhanced_results_report.md"
    with open(output_path, 'w') as f:
        f.write(report)
    
    print(f"\n📄 Detailed report saved to: {output_path}")
    
    # Final status
    ready_areas = len([r for r in results.values() if r['production_ready']])
    total_areas = len(results)
    
    if ready_areas == total_areas:
        print(f"\n🎉 SUCCESS: All {total_areas} Florida areas are production ready!")
        print(f"🚀 Florida can now be deployed to production!")
    else:
        print(f"\n⚠️  {ready_areas}/{total_areas} areas ready. Continue optimization for remaining areas.")

if __name__ == "__main__":
    main()
