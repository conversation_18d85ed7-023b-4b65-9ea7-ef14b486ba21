#!/usr/bin/env python3
"""
Legal Rules Coverage Audit System

This script performs comprehensive coverage analysis to ensure all deadline-bearing
provisions from authoritative legal sources are captured in the extracted rules.

Usage:
    python scripts/coverage_audit/audit.py --mode full
    python scripts/coverage_audit/audit.py --mode quick --jurisdiction TX_STATE
"""

import argparse
import yaml
import json
import re
import os
import sys
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from collections import defaultdict

@dataclass
class CoverageResult:
    source_name: str
    jurisdiction: str
    practice_area: str
    expected_deadlines: int
    extracted_deadlines: int
    coverage_percentage: float
    gap_count: int
    status: str  # PASS, WARN, FAIL
    missing_areas: List[str]

@dataclass
class QualityMetrics:
    total_rules: int
    calendar_deadline_rate: float
    clear_trigger_rate: float
    duplicate_rate: float
    false_positive_rate: float

class CoverageAuditor:
    def __init__(self, config_path: str = "scripts/coverage_audit/canonical_sources.yaml"):
        self.config_path = config_path
        self.canonical_sources = self._load_canonical_sources()
        self.coverage_thresholds = self.canonical_sources.get('coverage_thresholds', {})
        self.quality_thresholds = self.canonical_sources.get('quality_thresholds', {})
        
    def _load_canonical_sources(self) -> Dict:
        """Load the canonical sources configuration"""
        try:
            with open(self.config_path, 'r') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            print(f"❌ Canonical sources config not found: {self.config_path}")
            sys.exit(1)
    
    def _load_extracted_rules(self, jurisdiction: str, practice_area: str) -> Dict:
        """Load extracted rules for a specific jurisdiction and practice area"""
        # Try different file patterns
        patterns = [
            f"rules/{jurisdiction.lower()}_{practice_area.lower()}.yaml",
            f"rules/{jurisdiction}/processed/{practice_area.lower()}_deadlines_gemini_production_ready.yaml",
            f"rules/{jurisdiction.lower()}/processed/{practice_area.lower()}_deadlines_gemini_production_ready.yaml"
        ]
        
        for pattern in patterns:
            if os.path.exists(pattern):
                try:
                    with open(pattern, 'r') as f:
                        return yaml.safe_load(f)
                except Exception as e:
                    print(f"⚠️  Error loading {pattern}: {e}")
                    continue
        
        print(f"⚠️  No extracted rules found for {jurisdiction} {practice_area}")
        return {}
    
    def _count_deadline_rules(self, rules_data: Dict) -> int:
        """Count the number of deadline rules in extracted data"""
        if not rules_data or 'deadline_categories' not in rules_data:
            return 0
            
        total_count = 0
        for category_name, category_data in rules_data['deadline_categories'].items():
            if 'rules' in category_data:
                total_count += len(category_data['rules'])
        
        return total_count
    
    def _analyze_source_coverage(self, jurisdiction: str, practice_area: str, source_config: Dict) -> CoverageResult:
        """Analyze coverage for a specific source"""
        extracted_rules = self._load_extracted_rules(jurisdiction, practice_area)
        extracted_count = self._count_deadline_rules(extracted_rules)
        expected_count = source_config.get('estimated_deadlines', 0)
        
        if expected_count == 0:
            coverage_percentage = 0.0
            status = "UNKNOWN"
        else:
            coverage_percentage = (extracted_count / expected_count) * 100
            
            # Determine status based on priority and thresholds
            priority = source_config.get('priority', 'medium')
            threshold_key = f"{priority}_sources" if priority in ['critical', 'high'] else 'medium_priority'
            threshold = self.coverage_thresholds.get(threshold_key, 0.70) * 100
            
            if coverage_percentage >= threshold:
                status = "PASS"
            elif coverage_percentage >= threshold * 0.8:  # Within 20% of threshold
                status = "WARN"
            else:
                status = "FAIL"
        
        gap_count = max(0, expected_count - extracted_count)
        
        return CoverageResult(
            source_name=source_config.get('name', 'Unknown'),
            jurisdiction=jurisdiction,
            practice_area=practice_area,
            expected_deadlines=expected_count,
            extracted_deadlines=extracted_count,
            coverage_percentage=coverage_percentage,
            gap_count=gap_count,
            status=status,
            missing_areas=source_config.get('missing_sources', [])
        )
    
    def audit_jurisdiction(self, jurisdiction: str) -> List[CoverageResult]:
        """Audit coverage for all practice areas in a jurisdiction"""
        results = []

        # Handle case variations
        jurisdiction_key = jurisdiction.lower()
        if jurisdiction_key not in self.canonical_sources:
            print(f"❌ Jurisdiction {jurisdiction} not found in canonical sources")
            print(f"Available jurisdictions: {list(self.canonical_sources.keys())}")
            return results
        
        jurisdiction_config = self.canonical_sources[jurisdiction_key]
        
        for practice_area, area_config in jurisdiction_config.items():
            if 'sources' not in area_config:
                continue
                
            print(f"\n🔍 Auditing {jurisdiction.upper()} {practice_area.upper()}...")
            
            for source_config in area_config['sources']:
                result = self._analyze_source_coverage(jurisdiction.upper(), practice_area, source_config)
                results.append(result)
                
                # Print immediate feedback
                status_emoji = {"PASS": "✅", "WARN": "⚠️", "FAIL": "❌", "UNKNOWN": "❓"}
                print(f"  {status_emoji.get(result.status, '❓')} {result.source_name}: "
                      f"{result.extracted_deadlines}/{result.expected_deadlines} "
                      f"({result.coverage_percentage:.1f}%)")
        
        return results
    
    def generate_coverage_report(self, results: List[CoverageResult]) -> str:
        """Generate a comprehensive coverage report"""
        report = []
        report.append("# Legal Rules Coverage Audit Report")
        report.append(f"Generated: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # Summary statistics
        total_sources = len(results)
        passed = len([r for r in results if r.status == "PASS"])
        warned = len([r for r in results if r.status == "WARN"])
        failed = len([r for r in results if r.status == "FAIL"])

        report.append("## Summary")
        report.append(f"- **Total Sources Audited**: {total_sources}")

        if total_sources > 0:
            report.append(f"- **Passed**: {passed} ({passed/total_sources*100:.1f}%)")
            report.append(f"- **Warnings**: {warned} ({warned/total_sources*100:.1f}%)")
            report.append(f"- **Failed**: {failed} ({failed/total_sources*100:.1f}%)")
        else:
            report.append("- **No sources found to audit**")
        report.append("")
        
        # Detailed results by jurisdiction
        by_jurisdiction = defaultdict(list)
        for result in results:
            by_jurisdiction[result.jurisdiction].append(result)
        
        for jurisdiction, jurisdiction_results in by_jurisdiction.items():
            report.append(f"## {jurisdiction}")
            report.append("")
            report.append("| Source | Practice Area | Expected | Extracted | Coverage | Status |")
            report.append("|--------|---------------|----------|-----------|----------|--------|")
            
            for result in jurisdiction_results:
                status_emoji = {"PASS": "✅", "WARN": "⚠️", "FAIL": "❌", "UNKNOWN": "❓"}
                report.append(f"| {result.source_name} | {result.practice_area} | "
                            f"{result.expected_deadlines} | {result.extracted_deadlines} | "
                            f"{result.coverage_percentage:.1f}% | "
                            f"{status_emoji.get(result.status, '❓')} {result.status} |")
            
            report.append("")
            
            # Missing sources
            missing_sources = []
            for result in jurisdiction_results:
                missing_sources.extend(result.missing_areas)
            
            if missing_sources:
                report.append(f"### Missing Sources for {jurisdiction}")
                for source in set(missing_sources):
                    report.append(f"- {source}")
                report.append("")
        
        # Recommendations
        report.append("## Recommendations")
        
        critical_failures = [r for r in results if r.status == "FAIL" and "critical" in str(r)]
        if critical_failures:
            report.append("### 🚨 Critical Issues")
            for result in critical_failures:
                report.append(f"- **{result.jurisdiction} {result.practice_area}**: "
                            f"{result.source_name} only {result.coverage_percentage:.1f}% covered "
                            f"({result.gap_count} missing deadlines)")
        
        report.append("### Next Steps")
        report.append("1. Address all FAILED sources before production deployment")
        report.append("2. Investigate WARNING sources for potential gaps")
        report.append("3. Acquire missing source documents")
        report.append("4. Re-run extraction on incomplete sources")
        report.append("5. Validate with practicing attorneys")
        
        return "\n".join(report)

def main():
    parser = argparse.ArgumentParser(description="Legal Rules Coverage Audit")
    parser.add_argument("--mode", choices=["full", "quick"], default="quick",
                       help="Audit mode: full (all jurisdictions) or quick (specified jurisdiction)")
    parser.add_argument("--jurisdiction", help="Specific jurisdiction to audit (e.g., TX_STATE, FL_STATE)")
    parser.add_argument("--output", help="Output file for report (default: stdout)")
    parser.add_argument("--fail-threshold", type=float, default=0.10,
                       help="Fail build if any critical source below this coverage (default: 0.10)")
    
    args = parser.parse_args()
    
    auditor = CoverageAuditor()
    
    if args.mode == "full":
        # Audit all jurisdictions
        all_results = []
        for jurisdiction in ["texas", "florida"]:
            results = auditor.audit_jurisdiction(jurisdiction)
            all_results.extend(results)
    else:
        # Audit specific jurisdiction
        if not args.jurisdiction:
            print("❌ --jurisdiction required for quick mode")
            sys.exit(1)
        all_results = auditor.audit_jurisdiction(args.jurisdiction)
    
    # Generate report
    report = auditor.generate_coverage_report(all_results)
    
    if args.output:
        with open(args.output, 'w') as f:
            f.write(report)
        print(f"📄 Report saved to {args.output}")
    else:
        print(report)
    
    # Check for critical failures
    critical_failures = [r for r in all_results 
                        if r.status == "FAIL" and r.coverage_percentage < args.fail_threshold * 100]
    
    if critical_failures:
        print(f"\n❌ BUILD FAILED: {len(critical_failures)} critical sources below {args.fail_threshold*100}% coverage")
        sys.exit(1)
    else:
        print(f"\n✅ Coverage audit passed!")

if __name__ == "__main__":
    main()
