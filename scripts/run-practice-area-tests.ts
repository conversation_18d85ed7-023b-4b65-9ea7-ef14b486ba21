#!/usr/bin/env tsx

import { spawn } from 'child_process';
import { join } from 'path';

interface TestResult {
  testFile: string;
  passed: boolean;
  duration: number;
  output: string;
}

async function runTest(testFile: string): Promise<TestResult> {
  return new Promise((resolve) => {
    const startTime = Date.now();
    
    const testProcess = spawn('npm', ['test', '--', testFile, '--verbose'], {
      cwd: process.cwd(),
      stdio: 'pipe'
    });

    let output = '';
    
    testProcess.stdout.on('data', (data) => {
      output += data.toString();
    });
    
    testProcess.stderr.on('data', (data) => {
      output += data.toString();
    });

    testProcess.on('close', (code) => {
      const duration = Date.now() - startTime;
      resolve({
        testFile,
        passed: code === 0,
        duration,
        output
      });
    });
  });
}

async function runPracticeAreaTests() {
  console.log('🧪 Running Practice Area Test Suite...\n');

  const testFiles = [
    'test/practice-areas/fl-personal-injury.spec.ts',
    'test/practice-areas/fl-criminal-defense.spec.ts', 
    'test/practice-areas/fl-family-law.spec.ts',
    'test/practice-areas/tx-criminal-defense.spec.ts',
    'test/practice-areas/tx-family-law.spec.ts',
    'test/practice-areas/integration.spec.ts'
  ];

  const results: TestResult[] = [];
  let totalDuration = 0;

  for (const testFile of testFiles) {
    const testName = testFile.replace('test/practice-areas/', '').replace('.spec.ts', '');
    console.log(`📋 Running ${testName}...`);
    
    const result = await runTest(testFile);
    results.push(result);
    totalDuration += result.duration;

    if (result.passed) {
      console.log(`✅ ${testName} passed (${result.duration}ms)`);
    } else {
      console.log(`❌ ${testName} failed (${result.duration}ms)`);
    }
  }

  console.log('\n📊 TEST SUITE RESULTS');
  console.log('=' .repeat(60));

  const passedTests = results.filter(r => r.passed);
  const failedTests = results.filter(r => r.failed);

  console.log(`\n🎯 SUMMARY:`);
  console.log(`Total tests: ${results.length}`);
  console.log(`Passed: ${passedTests.length} ✅`);
  console.log(`Failed: ${failedTests.length} ${failedTests.length > 0 ? '❌' : ''}`);
  console.log(`Total duration: ${totalDuration}ms`);
  console.log(`Average duration: ${Math.round(totalDuration / results.length)}ms`);

  if (failedTests.length > 0) {
    console.log(`\n❌ FAILED TESTS:`);
    failedTests.forEach(test => {
      console.log(`  - ${test.testFile}`);
    });

    console.log(`\n📝 FAILURE DETAILS:`);
    failedTests.forEach(test => {
      console.log(`\n${test.testFile}:`);
      console.log('-'.repeat(40));
      // Show last 20 lines of output for debugging
      const lines = test.output.split('\n');
      const lastLines = lines.slice(-20);
      console.log(lastLines.join('\n'));
    });
  }

  console.log(`\n✅ PASSED TESTS:`);
  passedTests.forEach(test => {
    console.log(`  - ${test.testFile} (${test.duration}ms)`);
  });

  // Calculate confidence level
  const passRate = (passedTests.length / results.length) * 100;
  console.log(`\n🎯 CONFIDENCE LEVEL: ${passRate.toFixed(1)}%`);

  if (passRate >= 90) {
    console.log('🚀 EXCELLENT - Ready for production!');
  } else if (passRate >= 80) {
    console.log('🔧 GOOD - Minor issues to fix');
  } else if (passRate >= 70) {
    console.log('⚠️  NEEDS WORK - Several issues to address');
  } else {
    console.log('🚨 CRITICAL - Major issues need fixing');
  }

  console.log('\n🎉 Practice area test suite completed!');
  
  // Exit with error code if any tests failed
  process.exit(failedTests.length > 0 ? 1 : 0);
}

runPracticeAreaTests().catch(console.error);
