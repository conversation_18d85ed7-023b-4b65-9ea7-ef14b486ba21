#!/usr/bin/env tsx

import { loadRules, getRules, PracticeArea } from '../src/loaders/ruleLoader.js';

interface TriggerInfo {
  code: string;
  description: string;
  deadlineCount: number;
  sampleDeadlines: string[];
}

interface PracticeAreaInfo {
  jurisdiction: string;
  practiceArea: string;
  triggerCount: number;
  triggers: TriggerInfo[];
}

async function auditTriggerCodes() {
  console.log('🔍 Auditing Trigger Codes Across Practice Areas...\n');

  const practiceAreas: PracticeAreaInfo[] = [];

  // Define the practice areas we want to audit
  const areasToAudit = [
    { jurisdiction: 'FL_STATE', practiceArea: PracticeArea.PERSONAL_INJURY, name: 'FL Personal Injury' },
    { jurisdiction: 'FL_STATE', practiceArea: PracticeArea.CRIMINAL_DEFENSE, name: 'FL Criminal Defense' },
    { jurisdiction: 'FL_STATE', practiceArea: PracticeArea.FAMILY_LAW, name: 'FL Family Law' },
    { jurisdiction: 'TX_STATE', practiceArea: PracticeArea.CRIMINAL_DEFENSE, name: 'TX Criminal Defense' },
    { jurisdiction: 'TX_STATE', practiceArea: PracticeArea.FAMILY_LAW, name: 'TX Family Law' },
  ];

  for (const area of areasToAudit) {
    console.log(`📋 Loading ${area.name}...`);
    
    try {
      await loadRules(area.jurisdiction, area.practiceArea);
      
      if (getRules(area.jurisdiction, area.practiceArea)) {
        const rules = getRules(area.jurisdiction, area.practiceArea);
        
        const triggers: TriggerInfo[] = rules.triggers?.map((trigger: any) => ({
          code: trigger.code,
          description: trigger.description || 'No description',
          deadlineCount: trigger.deadlines?.length || 0,
          sampleDeadlines: trigger.deadlines?.slice(0, 3).map((d: any) => d.code || d.description?.substring(0, 50)) || []
        })) || [];

        practiceAreas.push({
          jurisdiction: area.jurisdiction,
          practiceArea: area.practiceArea,
          triggerCount: triggers.length,
          triggers
        });

        console.log(`✅ ${area.name}: ${triggers.length} triggers`);
      } else {
        console.log(`❌ ${area.name}: Failed to load`);
      }
    } catch (error) {
      console.log(`❌ ${area.name}: Error - ${error}`);
    }
  }

  console.log('\n📊 TRIGGER CODE AUDIT RESULTS\n');
  console.log('=' .repeat(80));

  // Collect all unique trigger codes
  const allTriggerCodes = new Set<string>();
  const triggersByCode = new Map<string, Array<{jurisdiction: string, practiceArea: string}>>();

  practiceAreas.forEach(area => {
    area.triggers.forEach(trigger => {
      allTriggerCodes.add(trigger.code);
      
      if (!triggersByCode.has(trigger.code)) {
        triggersByCode.set(trigger.code, []);
      }
      triggersByCode.get(trigger.code)!.push({
        jurisdiction: area.jurisdiction,
        practiceArea: area.practiceArea
      });
    });
  });

  console.log(`\n🎯 SUMMARY:`);
  console.log(`Total unique trigger codes: ${allTriggerCodes.size}`);
  console.log(`Practice areas audited: ${practiceAreas.length}`);

  console.log(`\n📝 ALL TRIGGER CODES BY FREQUENCY:\n`);
  
  // Sort trigger codes by frequency (most common first)
  const sortedTriggers = Array.from(triggersByCode.entries())
    .sort((a, b) => b[1].length - a[1].length);

  sortedTriggers.forEach(([code, areas]) => {
    console.log(`${code} (${areas.length} practice areas)`);
    areas.forEach(area => {
      console.log(`  - ${area.jurisdiction} ${area.practiceArea}`);
    });
    console.log('');
  });

  console.log(`\n📋 DETAILED BREAKDOWN BY PRACTICE AREA:\n`);
  
  practiceAreas.forEach(area => {
    console.log(`${area.jurisdiction} - ${area.practiceArea.toUpperCase()}`);
    console.log(`Triggers: ${area.triggerCount}`);
    
    area.triggers.forEach(trigger => {
      console.log(`  • ${trigger.code}`);
      console.log(`    Description: ${trigger.description}`);
      console.log(`    Deadlines: ${trigger.deadlineCount}`);
      if (trigger.sampleDeadlines.length > 0) {
        console.log(`    Sample deadlines: ${trigger.sampleDeadlines.join(', ')}`);
      }
      console.log('');
    });
    console.log('-'.repeat(60));
  });

  // Expected trigger codes from tests
  const expectedTriggerCodes = [
    'SERVICE_OF_PROCESS',
    'STATE_ENTITY_SERVICE', 
    'ARRAIGNMENT',
    'CHARGES_FILED',
    'PETITION_SERVICE',
    'LAST_PLEADING_SERVICE',
    'ARREST_BOOKING',
    'DIVORCE_FILED',
    'DIVORCE_DECREED'
  ];

  console.log(`\n🎯 TRIGGER CODE MAPPING ANALYSIS:\n`);
  console.log('Expected codes from tests vs Actual codes in files:\n');

  expectedTriggerCodes.forEach(expectedCode => {
    if (allTriggerCodes.has(expectedCode)) {
      console.log(`✅ ${expectedCode} - FOUND`);
    } else {
      console.log(`❌ ${expectedCode} - MISSING`);
      
      // Try to find similar codes
      const similarCodes = Array.from(allTriggerCodes).filter(code => 
        code.includes(expectedCode.split('_')[0]) || 
        expectedCode.includes(code.split('_')[0])
      );
      
      if (similarCodes.length > 0) {
        console.log(`   Possible matches: ${similarCodes.join(', ')}`);
      }
    }
  });

  console.log('\n🎉 Trigger code audit completed!');
}

auditTriggerCodes().catch(console.error);
