#!/usr/bin/env tsx

import { loadRules, getRule, PracticeArea } from '../src/loaders/ruleLoader.js';
import { mapTriggerCode, getTriggerMapping } from '../src/loaders/triggerCodeMapper.js';

async function testTriggerMapping() {
  console.log('🔍 Testing Trigger Code Mapping...\n');

  // Load practice area rules
  await loadRules('FL_STATE', PracticeArea.PERSONAL_INJURY);
  await loadRules('TX_STATE', PracticeArea.FAMILY_LAW);

  console.log('📋 Testing Direct Mappings:');
  
  // Test direct mappings
  const directMappings = [
    'SERVICE_OF_PROCESS',
    'ARRAIGNMENT', 
    'CHARGES_FILED'
  ];

  directMappings.forEach(code => {
    const mapped = mapTriggerCode(code);
    console.log(`  ${code} → ${mapped} ${mapped === code ? '✅' : '❌'}`);
  });

  console.log('\n📋 Testing Mapped Codes:');
  
  // Test mapped codes
  const mappedCodes = [
    { expected: 'STATE_ENTITY_SERVICE', actual: 'SERVICE_OF_PROCESS' },
    { expected: 'DIVORCE_FILED', actual: 'DIVORCE_DECREED' }
  ];

  mappedCodes.forEach(({ expected, actual }) => {
    const mapped = mapTriggerCode(expected);
    console.log(`  ${expected} → ${mapped} ${mapped === actual ? '✅' : '❌'}`);
  });

  console.log('\n📋 Testing Rule Lookup with Mapping:');

  try {
    // Test FL Personal Injury with direct trigger
    console.log('\n🔍 FL Personal Injury - SERVICE_OF_PROCESS (direct):');
    const rule1 = getRule('FL_STATE', 'SERVICE_OF_PROCESS', PracticeArea.PERSONAL_INJURY);
    console.log(`  ✅ Found rule with ${rule1.deadlines?.length || 0} deadlines`);
    
    // Test FL Personal Injury with mapped trigger
    console.log('\n🔍 FL Personal Injury - STATE_ENTITY_SERVICE (mapped):');
    const rule2 = getRule('FL_STATE', 'STATE_ENTITY_SERVICE', PracticeArea.PERSONAL_INJURY);
    console.log(`  ✅ Found rule with ${rule2.deadlines?.length || 0} deadlines`);
    console.log(`  📝 Actual trigger code: ${rule2.code}`);
    
    // Test TX Family Law with mapped trigger
    console.log('\n🔍 TX Family Law - DIVORCE_FILED (mapped):');
    const rule3 = getRule('TX_STATE', 'DIVORCE_FILED', PracticeArea.FAMILY_LAW);
    console.log(`  ✅ Found rule with ${rule3.deadlines?.length || 0} deadlines`);
    console.log(`  📝 Actual trigger code: ${rule3.code}`);

  } catch (error) {
    console.log(`  ❌ Error: ${error}`);
  }

  console.log('\n📋 Testing Mapping Information:');
  
  const mappingInfo = getTriggerMapping('STATE_ENTITY_SERVICE');
  if (mappingInfo) {
    console.log(`  STATE_ENTITY_SERVICE mapping:`);
    console.log(`    Expected: ${mappingInfo.expectedCode}`);
    console.log(`    Actual: ${mappingInfo.actualCode}`);
    console.log(`    Practice Areas: ${mappingInfo.practiceAreas.join(', ')}`);
    console.log(`    Description: ${mappingInfo.description}`);
  }

  console.log('\n🎉 Trigger code mapping test completed!');
}

testTriggerMapping().catch(console.error);
