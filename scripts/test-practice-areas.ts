#!/usr/bin/env tsx

import { loadRules, getRules, areRulesLoaded, PracticeArea, getAvailablePracticeAreas } from '../src/loaders/ruleLoader.js';

async function testPracticeAreas() {
  console.log('🧪 Testing Practice Area Loading...\n');

  // Test FL Personal Injury
  console.log('📋 Loading FL_STATE Personal Injury rules...');
  await loadRules('FL_STATE', PracticeArea.PERSONAL_INJURY);
  
  if (areRulesLoaded('FL_STATE', PracticeArea.PERSONAL_INJURY)) {
    const rules = getRules('FL_STATE', PracticeArea.PERSONAL_INJURY);
    console.log('✅ FL Personal Injury loaded successfully');
    console.log(`   - Practice Area: ${rules.meta.practice_area}`);
    console.log(`   - Triggers: ${rules.triggers?.length || 0}`);
    console.log(`   - First 3 triggers: ${rules.triggers?.slice(0, 3).map((t: any) => t.code).join(', ')}`);
  } else {
    console.log('❌ FL Personal Injury failed to load');
  }

  console.log('');

  // Test TX Family Law
  console.log('📋 Loading TX_STATE Family Law rules...');
  await loadRules('TX_STATE', PracticeArea.FAMILY_LAW);
  
  if (areRulesLoaded('TX_STATE', PracticeArea.FAMILY_LAW)) {
    const rules = getRules('TX_STATE', PracticeArea.FAMILY_LAW);
    console.log('✅ TX Family Law loaded successfully');
    console.log(`   - Practice Area: ${rules.meta.practice_area}`);
    console.log(`   - Triggers: ${rules.triggers?.length || 0}`);
    console.log(`   - First 3 triggers: ${rules.triggers?.slice(0, 3).map((t: any) => t.code).join(', ')}`);
  } else {
    console.log('❌ TX Family Law failed to load');
  }

  console.log('');

  // Test TX Criminal Defense
  console.log('📋 Loading TX_STATE Criminal Defense rules...');
  await loadRules('TX_STATE', PracticeArea.CRIMINAL_DEFENSE);
  
  if (areRulesLoaded('TX_STATE', PracticeArea.CRIMINAL_DEFENSE)) {
    const rules = getRules('TX_STATE', PracticeArea.CRIMINAL_DEFENSE);
    console.log('✅ TX Criminal Defense loaded successfully');
    console.log(`   - Practice Area: ${rules.meta.practice_area}`);
    console.log(`   - Triggers: ${rules.triggers?.length || 0}`);
    console.log(`   - First 3 triggers: ${rules.triggers?.slice(0, 3).map((t: any) => t.code).join(', ')}`);
  } else {
    console.log('❌ TX Criminal Defense failed to load');
  }

  console.log('');

  // Test available practice areas
  console.log('📋 Available practice areas:');
  const flAreas = getAvailablePracticeAreas('FL_STATE');
  const txAreas = getAvailablePracticeAreas('TX_STATE');
  
  console.log(`   - FL_STATE: ${flAreas.join(', ')}`);
  console.log(`   - TX_STATE: ${txAreas.join(', ')}`);

  console.log('\n🎉 Practice area loading test completed!');
}

testPracticeAreas().catch(console.error);
