#!/usr/bin/env python3
"""
Enhanced Florida Criminal Extraction Script
Generated by florida_enhanced_extraction.py
"""

import google.generativeai as genai
import PyPDF2
import yaml
import os
from datetime import datetime

# Configure Gemini
genai.configure(api_key=os.getenv('GEMINI_API_KEY'))
model = genai.GenerativeModel('gemini-2.0-flash-exp')

def extract_text_from_pdf(pdf_path):
    """Extract text from PDF with enhanced handling for OCR issues"""
    text = ""
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            for page in pdf_reader.pages:
                page_text = page.extract_text()
                # Clean up common OCR issues
                page_text = page_text.replace('\n', ' ')  # Fix line breaks in deadlines
                page_text = ' '.join(page_text.split())    # Normalize whitespace
                text += page_text + "\n\n"
    except Exception as e:
        print(f"Error extracting PDF: {e}")
        return ""
    return text

def extract_deadlines():
    """Extract deadlines using enhanced Gemini prompt"""
    
    print("🔍 Extracting text from PDF...")
    pdf_text = extract_text_from_pdf("rules/florida/source_pdfs/florida_criminal_procedure_2025.pdf")
    
    if not pdf_text:
        print("❌ Failed to extract text from PDF")
        return
    
    print(f"📄 Extracted {len(pdf_text)} characters from PDF")
    
    # Enhanced prompt for Florida extraction
    prompt = """
You are a legal expert analyzing Florida criminal procedure rules to extract ALL calendar deadlines.

CRITICAL INSTRUCTIONS:
1. Extract EVERY deadline that involves a specific time period (days, months, years, hours)
2. Pay special attention to broken text patterns like "60 \ndays" or "May  22, 202 5"
3. Look for deadlines in discovery, motion practice, trial preparation, and case management
4. Include deadlines that may be split across lines or have formatting issues

FLORIDA-SPECIFIC PATTERNS TO LOOK FOR:
- Service deadlines (20 days, 30 days, 40 days)
- Discovery deadlines (30 days, 45 days, 60 days, 90 days)
- Motion deadlines (5 days, 10 days, 15 days, 20 days)
- Trial preparation deadlines (10 days, 15 days, 30 days)
- Case management deadlines (60 days, 90 days, 120 days)
- Appeal deadlines (30 days, 60 days)

TEXT QUALITY ISSUES TO HANDLE:
- Deadlines split across lines: "60 \ndays" should be read as "60 days"
- Extra spaces: "within  30  days" should be read as "within 30 days"
- Date formatting issues: "May  22, 202 5" indicates OCR problems
- Rule numbering issues: Look for patterns like "Rule 1.140" even if formatting is poor

EXTRACTION REQUIREMENTS:
- Extract at least 200+ deadlines for civil, 250+ for criminal, 150+ for family
- Focus on actionable deadlines that practitioners must track
- Include both mandatory and permissive deadlines
- Capture deadlines for all parties (plaintiff, defendant, court, third parties)

CATEGORIES TO PRIORITIZE:
1. Pleading and Response Deadlines
2. Discovery Deadlines  
3. Motion Practice Deadlines
4. Trial and Hearing Deadlines
5. Case Management Deadlines
6. Appeal and Post-Trial Deadlines
7. Service and Notice Deadlines
8. Emergency and Expedited Deadlines

For each deadline, provide:
- Rule number
- Exact deadline (e.g., "20 days", "within 30 days")
- Clear description of what must be done
- Trigger event that starts the deadline
- Consequences of missing the deadline
- Any available extensions

Be comprehensive - missing deadlines in production could result in malpractice claims.

    
    Now analyze this Florida criminal procedure text and extract ALL calendar deadlines:
    
    {pdf_text}
    
    Provide the output in YAML format following this structure:
    
    jurisdiction: Florida
    court_type: Criminal
    practice_area: Personal Injury
    last_updated: '2025-06-13'
    source: Florida Rules of Criminal Procedure
    extraction_method: Gemini 2.0 Flash Enhanced Analysis
    extraction_date: '2025-06-13T12:00:00'
    total_deadlines_extracted: [COUNT]
    deadline_categories:
      [category_name]:
        description: [Category description]
        rules:
          [rule_id]:
            rule: [Rule number]
            deadline: [Specific deadline]
            description: [What must be done]
            trigger: [Event that starts deadline]
            consequence: [What happens if missed]
            extensions: [Available extensions]
            trigger_event: [Standardized trigger type]
            trigger_parameters:
              original_text: [Original trigger text]
              jurisdiction: Florida
    """
    
    print("🤖 Sending to Gemini for enhanced extraction...")
    
    try:
        # Split into chunks if text is too long
        max_chunk_size = 100000  # Adjust based on Gemini limits
        
        if len(pdf_text) > max_chunk_size:
            print(f"📝 Text too long, processing in chunks...")
            chunks = [pdf_text[i:i+max_chunk_size] for i in range(0, len(pdf_text), max_chunk_size)]
            all_results = []
            
            for i, chunk in enumerate(chunks):
                print(f"Processing chunk {i+1}/{len(chunks)}...")
                chunk_prompt = prompt.format(pdf_text=chunk)
                response = model.generate_content(chunk_prompt)
                all_results.append(response.text)
            
            # Combine results (this would need manual review)
            final_result = "\n".join(all_results)
        else:
            full_prompt = prompt.format(pdf_text=pdf_text)
            response = model.generate_content(full_prompt)
            final_result = response.text
        
        # Save result
        output_path = "rules/florida/processed/criminal_deadlines_enhanced_extraction.yaml"
        with open(output_path, 'w') as f:
            f.write(final_result)

        print(f"✅ Enhanced extraction completed: {output_path}")
        
        # Try to parse and count deadlines
        try:
            result_data = yaml.safe_load(final_result)
            total_extracted = result_data.get('total_deadlines_extracted', 0)
            print(f"📊 Total deadlines extracted: {total_extracted}")
        except:
            print("⚠️  Could not parse YAML to count deadlines")
            
    except Exception as e:
        print(f"❌ Error during extraction: {e}")

if __name__ == "__main__":
    extract_deadlines()
