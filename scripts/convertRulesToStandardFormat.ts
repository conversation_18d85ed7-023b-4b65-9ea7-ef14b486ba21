#!/usr/bin/env ts-node

import * as fs from 'fs';
import * as path from 'path';
import * as yaml from 'js-yaml';

interface ExtractedRule {
  rule: string;
  deadline: string;
  description: string;
  trigger: string;
  consequence: string;
  extensions: string;
  trigger_event: string;
  trigger_parameters: {
    original_text: string;
    jurisdiction: string;
  };
}

interface ExtractedRuleFile {
  jurisdiction: string;
  court_type: string;
  practice_area: string;
  last_updated: string;
  source: string;
  extraction_method: string;
  extraction_date: string;
  total_deadlines_extracted: number;
  deadline_categories: Record<string, {
    description: string;
    rules: Record<string, ExtractedRule>;
  }>;
}

interface StandardRule {
  meta: {
    jurisdiction: string;
    version: string;
    source: string;
    practice_area: string;
    court_type: string;
  };
  triggers: Array<{
    code: string;
    description: string;
    deadlines: Array<{
      code: string;
      offset: number;
      offsetUnit: 'calendar' | 'business';
      citation: string;
      description: string;
    }>;
  }>;
}

function parseDeadlineOffset(deadlineText: string): { offset: number; unit: 'calendar' | 'business' } {
  const text = deadlineText.toLowerCase();
  
  // Extract number
  const numberMatch = text.match(/(\d+)/);
  if (!numberMatch) {
    throw new Error(`Could not parse deadline: ${deadlineText}`);
  }
  
  const offset = parseInt(numberMatch[1]);
  
  // Determine if business or calendar days
  const unit = text.includes('business') || text.includes('working') ? 'business' : 'calendar';
  
  return { offset, unit };
}

function mapTriggerEvent(triggerEvent: string, originalText: string): string {
  const mapping: Record<string, string> = {
    'serviceCompleted': 'SERVICE_OF_PROCESS',
    'eventOccurs': 'EVENT_OCCURS',
    'documentFiled': 'DOCUMENT_FILED',
    'motionFiled': 'MOTION_FILED',
    'arrestBooking': 'ARREST_BOOKING',
    'chargesFiled': 'CHARGES_FILED',
    'judgmentEntered': 'JUDGMENT_ENTERED',
    'divorceDecreed': 'DIVORCE_DECREED',
    'marriageCelebrated': 'MARRIAGE_CELEBRATED',
    'childBorn': 'CHILD_BORN',
    'noticeReceived': 'NOTICE_RECEIVED'
  };

  // Special cases based on original text
  if (originalText.toLowerCase().includes('service of original process')) {
    return 'SERVICE_OF_PROCESS';
  }
  if (originalText.toLowerCase().includes('service of petition')) {
    return 'PETITION_SERVICE';
  }
  if (originalText.toLowerCase().includes('last pleading')) {
    return 'LAST_PLEADING_SERVICE';
  }
  if (originalText.toLowerCase().includes('arraignment')) {
    return 'ARRAIGNMENT';
  }

  return mapping[triggerEvent] || 'EVENT_OCCURS';
}

function generateDeadlineCode(rule: string, description: string): string {
  const desc = description.toLowerCase();
  
  if (desc.includes('answer')) return 'ANSWER_DUE';
  if (desc.includes('jury trial') || desc.includes('jury demand')) return 'JURY_TRIAL_DEMAND';
  if (desc.includes('mandatory disclosure')) return 'MANDATORY_DISCLOSURE';
  if (desc.includes('death penalty')) return 'DEATH_PENALTY_NOTICE';
  if (desc.includes('motion to dismiss')) return 'MOTION_TO_DISMISS';
  if (desc.includes('counsel') && desc.includes('appointment')) return 'COUNSEL_APPOINTMENT';
  if (desc.includes('indictment') && desc.includes('objection')) return 'INDICTMENT_OBJECTION';
  if (desc.includes('divorce') && desc.includes('waiting')) return 'DIVORCE_WAITING_PERIOD';
  if (desc.includes('remarriage')) return 'REMARRIAGE_PROHIBITION';
  
  // Generate from rule number
  return `DEADLINE_${rule.replace(/[^A-Z0-9]/g, '_').toUpperCase()}`;
}

function convertExtractedRuleToStandard(extractedFile: ExtractedRuleFile): StandardRule {
  const triggers: StandardRule['triggers'] = [];
  const triggerMap = new Map<string, StandardRule['triggers'][0]>();

  // Process all rules from all categories
  for (const [categoryName, category] of Object.entries(extractedFile.deadline_categories)) {
    for (const [ruleKey, rule] of Object.entries(category.rules)) {
      try {
        // Skip rules without valid deadlines
        if (!rule.deadline || rule.deadline === 'N/A' || rule.deadline === 'Varies') {
          continue;
        }

        const triggerCode = mapTriggerEvent(rule.trigger_event, rule.trigger_parameters.original_text);
        const deadlineCode = generateDeadlineCode(rule.rule, rule.description);
        
        // Parse deadline offset
        const { offset, unit } = parseDeadlineOffset(rule.deadline);

        // Find or create trigger
        let trigger = triggerMap.get(triggerCode);
        if (!trigger) {
          trigger = {
            code: triggerCode,
            description: rule.trigger || 'Event trigger',
            deadlines: []
          };
          triggerMap.set(triggerCode, trigger);
          triggers.push(trigger);
        }

        // Add deadline to trigger
        trigger.deadlines.push({
          code: deadlineCode,
          offset,
          offsetUnit: unit,
          citation: rule.rule,
          description: rule.description
        });

      } catch (error) {
        console.warn(`Skipping rule ${ruleKey}: ${error}`);
      }
    }
  }

  return {
    meta: {
      jurisdiction: extractedFile.jurisdiction === 'Florida' ? 'FL_STATE' : 
                   extractedFile.jurisdiction === 'Texas' ? 'TX_STATE' : 
                   extractedFile.jurisdiction,
      version: extractedFile.last_updated,
      source: extractedFile.source,
      practice_area: extractedFile.practice_area,
      court_type: extractedFile.court_type
    },
    triggers
  };
}

async function convertRuleFiles() {
  const rulesDir = path.join(process.cwd(), 'rules');
  
  // Florida files to convert
  const floridaFiles = [
    'rules/florida/processed/civil_deadlines_gemini_2_0_flash_production_ready.yaml',
    'rules/florida/processed/criminal_deadlines_gemini_production_ready.yaml',
    'rules/florida/processed/family_deadlines_gemini_production_ready.yaml'
  ];

  // Texas files to convert
  const texasFiles = [
    'rules/Texas/processed/criminal_deadlines_gemini_production_ready.yaml',
    'rules/Texas/processed/family_deadlines_gemini_production_ready.yaml'
  ];

  const allFiles = [...floridaFiles, ...texasFiles];

  for (const filePath of allFiles) {
    const fullPath = path.join(process.cwd(), filePath);
    
    if (!fs.existsSync(fullPath)) {
      console.warn(`File not found: ${fullPath}`);
      continue;
    }

    try {
      console.log(`Converting ${filePath}...`);
      
      const content = fs.readFileSync(fullPath, 'utf8');
      const extractedFile = yaml.load(content) as ExtractedRuleFile;
      
      const standardRule = convertExtractedRuleToStandard(extractedFile);
      
      // Generate output filename
      const jurisdiction = standardRule.meta.jurisdiction;
      const practiceArea = standardRule.meta.practice_area.toLowerCase().replace(/\s+/g, '_');
      const outputFileName = `${jurisdiction}_${practiceArea}.yaml`;
      const outputPath = path.join(rulesDir, outputFileName);
      
      // Write converted file
      const yamlContent = yaml.dump(standardRule, { 
        indent: 2,
        lineWidth: 120,
        noRefs: true
      });
      
      fs.writeFileSync(outputPath, yamlContent);
      
      console.log(`✅ Converted to ${outputFileName} (${standardRule.triggers.length} triggers, ${standardRule.triggers.reduce((sum, t) => sum + t.deadlines.length, 0)} deadlines)`);
      
    } catch (error) {
      console.error(`❌ Error converting ${filePath}:`, error);
    }
  }
}

// Run the conversion
if (require.main === module) {
  convertRuleFiles().catch(console.error);
}

export { convertRuleFiles, convertExtractedRuleToStandard };
