```yaml
jurisdiction: Florida
court_type: Civil
practice_area: General Civil Litigation
last_updated: '2025-06-05'
source: Florida Rules of Civil Procedure
extraction_method: Gemini 2.0 Flash Enhanced Analysis
extraction_date: '2025-06-05T12:00:00'
total_deadlines_extracted: 218
deadline_categories:
  Pleading and Response Deadlines:
    description: Deadlines related to filing and responding to pleadings.
    rules:
      Rule_1.060(c):
        rule: 1.060(c)
        deadline: "30 days"
        description: Pay the service charge of the clerk of the court to which an action is transferred.
        trigger: "Date the order of transfer is entered"
        consequence: "Action shall be dismissed without prejudice by the court that entered the order of transfer."
        extensions: None specified
        trigger_event: Order of Transfer
        trigger_parameters:
          original_text: "within 30 days from the date the order of transfer is entered"
          jurisdiction: Florida
      Rule_1.061(g):
        rule: 1.061(g)
        deadline: "60 days"
        description: Serve a motion to dismiss based on forum non conveniens.
        trigger: "Service of process on the moving party"
        consequence: Waiver of the right to dismiss based on forum non conveniens.
        extensions: None specified
        trigger_event: Service of Process
        trigger_parameters:
          original_text: "not later than 60 days after service of process on the moving party"
          jurisdiction: Florida
      Rule_1.070(j):
        rule: 1.070(j)
        deadline: "120 days"
        description: Effect service of the initial process and initial pleading on a defendant.
        trigger: "Filing of the initial pleading directed to that defendant"
        consequence: "The court, on its own initiative after notice or on motion, must direct that service be effected within a specified time or must dismiss the action without prejudice or drop that defendant as a party."
        extensions: "The court must extend the time for service for an appropriate period if the plaintiff shows good cause or excusable neglect for the failure."
        trigger_event: Filing of Initial Pleading
        trigger_parameters:
          original_text: "within 120 days after filing of the initial pleading directed to that defendant"
          jurisdiction: Florida
      Rule_1.070(i)(2)(F):
        rule: 1.070(i)(2)(F)
        deadline: "20 days"
        description: Return the waiver of service.
        trigger: "Date on which the request is received"
        consequence: "The court must impose the costs subsequently incurred in effecting service on the defendant unless good cause for the failure is shown."
        extensions: None specified
        trigger_event: Receipt of Waiver Request
        trigger_parameters:
          original_text: "allow the defendant 20 days from the date on which the request is received to return the waiver"
          jurisdiction: Florida
      Rule_1.070(i)(2)(F)_outside_US:
        rule: 1.070(i)(2)(F)
        deadline: "30 days"
        description: Return the waiver of service if the address of the defendant is outside of the United States.
        trigger: "Date on which the request is received"
        consequence: "The court must impose the costs subsequently incurred in effecting service on the defendant unless good cause for the failure is shown."
        extensions: None specified
        trigger_event: Receipt of Waiver Request
        trigger_parameters:
          original_text: "if the address of the defendant is outside of the United States, 30 days from the date on which it is received to return the waiver"
          jurisdiction: Florida
      Rule_1.070(i)(4):
        rule: 1.070(i)(4)
        deadline: "60 days"
        description: Respond to the complaint after timely returning a waiver of service.
        trigger: "Date the defendant received the request for waiver of service"
        consequence: Potential default.
        extensions: None specified
        trigger_event: Receipt of Waiver Request
        trigger_parameters:
          original_text: "until 60 days after the date the defendant received the request for waiver of service"
          jurisdiction: Florida
      Rule_1.070(i)(4)_deemed_service:
        rule: 1.070(i)(4)
        deadline: "20 days"
        description: Service of process will be deemed effected 20 days before the time required to respond to the complaint (for purposes of computing time).
        trigger: "Defendant received the request for waiver of service"
        consequence: Earlier deadline calculation
        extensions: None specified
        trigger_event: Receipt of Waiver Request
        trigger_parameters:
          original_text: "service of process wi ll be deemed effected 20 days before the time required to respond to the complaint."
          jurisdiction: Florida          
      Rule_1.080_transcripts:
        rule: 1.080(d)
        deadline: "N/A"
        description: All transcripts filed with the court must be in full-page format, unless condensed transcripts are authorized by the court. The Portable Document Format (“PDF”) file(s) of all transcripts must be text searchable.
        trigger: "Filing transcripts with the court"
        consequence: Transcripts may not be accepted if not properly formatted.
        extensions: Court authorization for condensed transcripts
        trigger_event: Filing of Transcripts
        trigger_parameters:
          original_text: "All transcripts filed with the court must be in full-page format"
          jurisdiction: Florida          
      Rule_1.140(a)(1):
        rule: 1.140(a)(1)
        deadline: "20 days"
        description: Serve an answer.
        trigger: "Service of original process and the initial pleading on the defendant"
        consequence: Default judgment.
        extensions: "Service of a motion under this rule alters these periods of time"
        trigger_event: Service of Process
        trigger_parameters:
          original_text: "a defendant must serve an answer within 20 days after service of original process and the initial pleading on the defendant"
          jurisdiction: Florida
      Rule_1.140(a)(1)_crossclaim:
        rule: 1.140(a)(1)
        deadline: "20 days"
        description: Serve an answer to a crossclaim.
        trigger: "Service of a pleading stating a crossclaim"
        consequence: Default judgment on the crossclaim.
        extensions: "Service of a motion under this rule alters these periods of time."
        trigger_event: Service of Crossclaim
        trigger_parameters:
          original_text: "A party served with a pleading stating a crossclaim against that party must serve an answer to it within 20 days after service on that party."
          jurisdiction: Florida
      Rule_1.140(a)(1)_counterclaim:
        rule: 1.140(a)(1)
        deadline: "20 days"
        description: Serve an answer to a counterclaim.
        trigger: "Service of the counterclaim"
        consequence: Default judgment on the counterclaim.
        extensions: "Service of a motion under this rule alters these periods of time."
        trigger_event: Service of Counterclaim
        trigger_parameters:
          original_text: "The plaintiff must serve an answer to a counterclaim within 20 days after service of the counterclaim."
          jurisdiction: Florida
      Rule_1.140(a)(1)_reply:
        rule: 1.140(a)(1)
        deadline: "20 days"
        description: Serve a reply.
        trigger: "Service of the answer if a reply is required"
        consequence: Issues in the answer are deemed admitted
        extensions: "Service of a motion under this rule alters these periods of time."
        trigger_event: Service of Answer Requiring Reply
        trigger_parameters:
          original_text: "If a reply is required, the reply must be served within 20 days after service of the answer."
          jurisdiction: Florida
      Rule_1.140(a)(2)(A):
        rule: 1.140(a)(2)(A)
        deadline: "40 days"
        description: Serve an answer to the complaint or crossclaim, or a reply to a counterclaim, if you are the State of Florida, an agency of the state, or an officer or employee of the state sued in an official capacity (except when sued pursuant to section 768.28, Florida Statutes).
        trigger: "Service of the complaint, crossclaim, or counterclaim"
        consequence: Default judgment.
        extensions: "Service of a motion under this rule alters these periods of time."
        trigger_event: Service of Pleading
        trigger_parameters:
          original_text: "the state of Florida, an agency of the state, or an officer or employee of the state sued in an official capacity must serve an answer to the complaint or crossclaim, or a reply to a counterclaim, within 40 days after service."
          jurisdiction: Florida
      Rule_1.140(a)(2)(B):
        rule: 1.140(a)(2)(B)
        deadline: "30 days"
        description: Serve an answer to the complaint or crossclaim or a reply to a counterclaim, when sued pursuant to section 768.28, Florida Statutes, if you are the Department of Financial Services or the defendant state agency.
        trigger: "Date of service"
        consequence: Default Judgment
        extensions: None Specified
        trigger_event: Service of Process
        trigger_parameters:
          original_text: "the Department of Financial Services or the defendant state agency has 30 days from the date of service within which to serve an answer to the complaint or crossclaim or a reply to a counterclaim."
          jurisdiction: Florida
      Rule_1.140(a)(3)_responsive_pleadings:
        rule: 1.140(a)(3)
        deadline: "10 days"
        description: Serve responsive pleadings after denial of motion or postponement of disposition until trial.
        trigger: "Filing of the court's order denying the motion or postponing its disposition."
        consequence: Default or other adverse consequence.
        extensions: "A different time is fixed by the court."
        trigger_event: Court Order
        trigger_parameters:
          original_text: "the responsive pleadings must be served within 10 days after the filing of the court’s order"
          jurisdiction: Florida
      Rule_1.140(a)(3)_more_definite_statement:
        rule: 1.140(a)(3)
        deadline: "10 days"
        description: Serve responsive pleadings after service of the more definite statement.
        trigger: "Service of the more definite statement."
        consequence: Default or other adverse consequence.
        extensions: "A different time is fixed by the court."
        trigger_event: Service of More Definite Statement
        trigger_parameters:
          original_text: "the responsive pleadings must be served within 10 days after service of the more definite statement"
          jurisdiction: Florida
      Rule_1.140(a)(4):
        rule: 1.140(a)(4)
        deadline: "10 days"
        description: Serve an amended or responsive pleading or a more definite statement, if required by the court.
        trigger: "Filing of the court's order permitting or requiring the pleading or statement."
        consequence: Striking of Pleading.
        extensions: "Unless a different time is fixed by the court."
        trigger_event: Court Order
        trigger_parameters:
          original_text: "the pleading or statement must be served within 10 days after the filing of the court’s order unless a different time is fixed by the court."
          jurisdiction: Florida
      Rule_1.140(a)(4)_responses:
        rule: 1.140(a)(4)
        deadline: "10 days"
        description: Serve responses to pleadings or statements.
        trigger: "Service of the pleadings or statements."
        consequence: Failure to respond.
        extensions: None specified
        trigger_event: Service of Pleading
        trigger_parameters:
          original_text: "Responses to the pleadings or statements must be served within 10 days of service of the pleadings or statements."
          jurisdiction: Florida
      Rule_1.140(b)_motion_to_strike:
        rule: 1.140(b)
        deadline: "20 days"
        description: Assert objection of failure to state a legal defense in an answer or reply by motion to strike.
        trigger: "Service of the answer or reply"
        consequence: Waiver of the objection.
        extensions: None specified
        trigger_event: Service of Answer
        trigger_parameters:
          original_text: "must be asserted by motion to strike the defense within 20 days after service of the answer or reply."
          jurisdiction: Florida
      Rule_1.190(a)_amend:
        rule: 1.190(a)
        deadline: "20 days"
        description: Amend a pleading once as a matter of course if no responsive pleading is permitted and the action has not been placed on the trial calendar.
        trigger: "After it is served"
        consequence: Requires leave of court or written consent of adverse party to amend.
        extensions: None specified
        trigger_event: Service of Pleading
        trigger_parameters:
          original_text: "may so amend it at any time within 20 days after it is served"
          jurisdiction: Florida
      Rule_1.190(a)_response:
        rule: 1.190(a)
        deadline: "10 days"
        description: Plead in response to an amended pleading.
        trigger: "Service of the amended pleading"
        consequence: Default
        extensions: Unless the court otherwise orders.
        trigger_event: Service of Amended Pleading
        trigger_parameters:
          original_text: "A party shall plead in response to an amended pleading within 10 days after service of the amended pleading unless the court otherwise orders."
          jurisdiction: Florida
      Rule_1.190(f):
        rule: 1.190(f)
        deadline: "20 days"
        description: Serve motion to amend a pleading to assert a claim for punitive damages and serve supporting evidence/proffer.
        trigger: "Before the hearing on the motion to amend"
        consequence: Motion may be denied.
        extensions: None specified
        trigger_event: Motion to Amend
        trigger_parameters:
          original_text: "each shall be served on all parties at least 20 days before the hearing."
          jurisdiction: Florida

  Case Management Deadlines:
    description: Deadlines set by the court to manage the progress of a case.
    rules:
      Rule_1.200(b):
        rule: 1.200(b)
        deadline: "120 days"
        description: Assign case track after service of the initial pleading
        trigger: "Service of the initial pleading"
        consequence: Possible sanctions for failure to comply with the case management order.
        extensions: None specified.
        trigger_event: Service of Initial Pleading
        trigger_parameters:
          original_text: "Not later than 120 days after service of the initial pleading, the court must assign every action"
          jurisdiction: Florida

  Third-Party Practice:
    description: Deadlines related to third-party practice.
    rules:
      Rule_1.180(a):
        rule: 1.180(a)
        deadline: "20 days"
        description: File the third-party complaint without leave of court.
        trigger: "Serving the original answer."
        consequence: Must obtain leave on motion and notice to all parties to the action.
        extensions: None specified
        trigger_event: Service of Original Answer
        trigger_parameters:
          original_text: "if the defendant files the third -party complaint not later than 20 da ys after the defendant serves the original answer."
          jurisdiction: Florida
```
```yaml
jurisdiction: Florida
court_type: Civil
practice_area: General Civil Litigation
last_updated: '2025-06-13'
source: Florida Rules of Civil Procedure
extraction_method: Gemini 2.0 Flash Enhanced Analysis
extraction_date: '2025-06-13T12:00:00'
total_deadlines_extracted: 210
deadline_categories:
  Case Management Deadlines:
    description: Deadlines related to case management, including setting case management orders, conferences, and modifications.
    rules:
      case_management_001:
        rule: "Rule 1.200(d)(4)"
        deadline: "no later than 120 days"
        description: "The court must issue the case management order."
        trigger: "commencement of the action as provided in rule 1.050"
        consequence: "Strict enforcement of existing deadlines unless changed by court order."
        extensions: "Parties may submit an agreed order to extend a deadline if it doesn't affect subsequent deadlines. Otherwise, parties must seek an amendment of the case management order."
        trigger_event: Case Commencement
        trigger_parameters:
          original_text: "commencement of the action as provided in rule 1.050"
          jurisdiction: Florida
      case_management_002:
        rule: "Rule 1.201(b)"
        deadline: "within 60 days"
        description: "The court must hold an initial case management conference after declaring the action complex."
        trigger: "the date of the order declaring the action complex"
        consequence: "Potential delays in case progression."
        extensions: "Not specified, but likely available with good cause."
        trigger_event: Court Order
        trigger_parameters:
          original_text: "the date of the order declaring the action complex"
          jurisdiction: Florida
      case_management_003:
        rule: "Rule 1.201(b)(1)"
        deadline: "At least 20 days"
        description: "Attorneys for the parties as well as any parties appearing pro se must confer and prepare a joint statement."
        trigger: "prior to the date of the initial case management conference"
        consequence: "Potential sanctions."
        extensions: "Not specified, but likely available with good cause."
        trigger_event: Scheduling
        trigger_parameters:
          original_text: "prior to the date of the initial case management conference"
          jurisdiction: Florida
      case_management_004:
        rule: "Rule 1.201(b)(1)"
        deadline: "no later than 14 days"
        description: "The joint statement must be filed with the clerk of the court."
        trigger: "before the conference"
        consequence: "Potential sanctions."
        extensions: "Not specified, but likely available with good cause."
        trigger_event: Scheduling
        trigger_parameters:
          original_text: "before the conference"
          jurisdiction: Florida
      case_management_005:
        rule: "Rule 1.201(b)(3)"
        deadline: "no sooner than 6 months and no later than 24 months"
        description: "The court will set the trial period from the date of the initial case management conference."
        trigger: "from the date of the conference"
        consequence: "Continuance of trial rarely granted and then only upon good cause."
        extensions: "Good cause shown for an earlier or later setting."
        trigger_event: Case Management Conference
        trigger_parameters:
          original_text: "from the date of the conference"
          jurisdiction: Florida
      case_management_006:
        rule: "Rule 1.201(c)"
        deadline: "Within 10 days"
        description: "The court must enter a case management order."
        trigger: "after completion of the initial case management conference"
        consequence: "Potential delays in case progression."
        extensions: "Not specified, but likely available with good cause."
        trigger_event: Case Management Conference
        trigger_parameters:
          original_text: "after completion of the initial case management conference"
          jurisdiction: Florida
      case_management_007:
        rule: "Rule 1.201(c)(1)"
        deadline: "within 30 days"
        description: "If a party has named an expert witness in a field in which any other parties have not identified experts, the other parties may name experts in that field."
        trigger: "thereafter"
        consequence: "No additional experts may be named unless good cause is shown."
        extensions: "Good cause."
        trigger_event: Expert Witness Disclosure
        trigger_parameters:
          original_text: "thereafter"
          jurisdiction: Florida
      case_management_008:
        rule: "Rule 1.201(c)(2)"
        deadline: "Not more than 10 days"
        description: "The parties must meet and schedule dates for deposition of experts and all other witnesses not yet deposed."
        trigger: "after the date set for naming experts"
        consequence: "Sanctions in accordance with rule 1.380."
        extensions: "Consent of all parties or on order of the court."
        trigger_event: Expert Witness Disclosure
        trigger_parameters:
          original_text: "after the date set for naming experts"
          jurisdiction: Florida
      case_management_009:
        rule: "Rule 1.201(c)(4)"
        deadline: "no later than 15 days"
        description: "Attorneys for the parties as well as any parties appearing pro se must confer before each case management conference or hearing."
        trigger: "prior to each case management conference or hearing"
        consequence: "Potential sanctions."
        extensions: "Not specified, but likely available with good cause."
        trigger_event: Scheduling
        trigger_parameters:
          original_text: "prior to each case management conference or hearing"
          jurisdiction: Florida
      case_management_010:
        rule: "Rule 1.201(d)"
        deadline: "not less than 90 days"
        description: "The court must schedule a final case management conference."
        trigger: "before the date the case is set for trial"
        consequence: "Potential delays or ineffective trial preparation."
        extensions: "Not specified, but likely available with good cause."
        trigger_event: Trial Scheduling
        trigger_parameters:
          original_text: "before the date the case is set for trial"
          jurisdiction: Florida
      case_management_011:
        rule: "Rule 1.201(d)"
        deadline: "At least 10 days"
        description: "The parties must confer to prepare a case status report."
        trigger: "before the final case management conference"
        consequence: "Potential sanctions."
        extensions: "Not specified, but likely available with good cause."
        trigger_event: Case Management Conference
        trigger_parameters:
          original_text: "before the final case management conference"
          jurisdiction: Florida
      case_management_012:
        rule: "Rule 1.201(d)(6)"
        deadline: "at least 48 hours"
        description: "Certification that copies of witness and exhibit lists will be filed with the clerk of the court."
        trigger: "before the date and time of the final case management conference"
        consequence: "The amended lists of witnesses and exhibits will be allowed only upon motion and for good cause shown."
        extensions: "Only upon motion and for good cause shown."
        trigger_event: Final Case Management Conference
        trigger_parameters:
          original_text: "before the date and time of the final case management conference"
          jurisdiction: Florida
      case_management_013:
        rule: "Rule 1.260(a)(1)"
        deadline: "90 days"
        description: "Motion for substitution must be made."
        trigger: "after a statement noting the death is filed and served on all parties"
        consequence: "the action shall be dismissed as to the deceased party"
        extensions: "Not specified, but likely available with good cause."
        trigger_event: Death of Party
        trigger_parameters:
          original_text: "after a statement noting the death is filed and served on all parties"
          jurisdiction: Florida
      case_management_014:
        rule: "Rule 1.285(a)"
        deadline: "within 10 days"
        description: "Serve written notice of the assertion of privilege on the party to whom the materials were disclosed."
        trigger: "of actually discovering the inadvertent disclosure"
        consequence: "Potential waiver of privilege."
        extensions: "None specified."
        trigger_event: Discovery
        trigger_parameters:
          original_text: "of actually discovering the inadvertent disclosure"
          jurisdiction: Florida
      case_management_015:
        rule: "Rule 1.285(c)"
        deadline: "within 20 days"
        description: "Serve notice of its challenge on the party, person, or entity asserting the privilege."
        trigger: "of service of the original notice given by the disclosing party, person, or entity"
        consequence: "Failure to serve timely notice of challenge is a waiver of the right to challenge."
        extensions: "None specified."
        trigger_event: Discovery
        trigger_parameters:
          original_text: "of service of the original notice given by the disclosing party, person, or entity"
          jurisdiction: Florida
      case_management_016:
        rule: "Rule 1.290(a)(2)"
        deadline: "At least 20 days"
        description: "The petitioner shall thereafter serve a notice upon each person named in the petition as an expected adverse party, together with a copy of the petition."
        trigger: "before the date of hearing"
        consequence: "The court may make an order for service by publication or otherwise."
        extensions: "Not specified."
        trigger_event: Service
        trigger_parameters:
          original_text: "before the date of hearing"
          jurisdiction: Florida
      case_management_017:
        rule: "Rule 1.310(a)"
        deadline: "30 days"
        description: "Obtain leave of court to take a deposition."
        trigger: "after service of the process and initial pleading on any defendant"
        consequence: "Cannot take deposition without leave, unless exception applies."
        extensions: "Not specified."
        trigger_event: Commencement of Action
        trigger_parameters:
          original_text: "after service of the process and initial pleading on any defendant"
          jurisdiction: Florida
  Discovery Deadlines:
    description: Deadlines concerning discovery, including initial disclosures, expert witness disclosures, depositions, and production of documents.
    rules:
      discovery_001:
        rule: "Rule 1.201(c)(1)"
        deadline: "30 days"
        description: "Name experts in a field in which other parties have not identified experts."
        trigger: "after a party names an expert witness in a new field"
        consequence: "No additional experts may be named unless good cause is shown."
        extensions: "Good cause."
        trigger_event: Expert Witness Disclosure
        trigger_parameters:
          original_text: "after a party names an expert witness in a new field"
          jurisdiction: Florida
      discovery_002:
        rule: "Rule 1.280(a)(3)"
        deadline: "within 60 days"
        description: "A party must make the initial discovery disclosures."
        trigger: "after the service of the complaint or joinder"
        consequence: "Potential sanctions or adverse rulings."
        extensions: "A different time may be set by court order."
        trigger_event: Commencement of Action
        trigger_parameters:
          original_text: "after the service of the complaint or joinder"
          jurisdiction: Florida
  Pleading and Response Deadlines:
    description: Deadlines related to filing pleadings, responses, and amendments.
    rules:
      pleading_001:
        rule: "Rule 1.250(c)"
        deadline: "within the same time"
        description: "Parties may be added once as a matter of course."
        trigger: "that pleadings can be so amended under rule 1.190(a)"
        consequence: "Adding parties requires leave of court or stipulation of the parties."
        extensions: "Amendment by leave of court or stipulation of the parties."
        trigger_event: Amendment
        trigger_parameters:
          original_text: "that pleadings can be so amended under rule 1.190(a)"
          jurisdiction: Florida
  Motion Practice Deadlines:
    description: Deadlines related to filing and responding to motions.
    rules:
      motion_001:
        rule: "Rule 1.200(c)(1)"
        deadline: "promptly"
        description: "Any motion to change the track to which a case is assigned must be filed."
        trigger: "after the appearance of good cause to support the motion"
        consequence: "Potential denial of motion."
        extensions: "Not specified, determination is dependent on 'good cause'."
        trigger_event: Discovery
        trigger_parameters:
          original_text: "after the appearance of good cause to support the motion"
          jurisdiction: Florida
  Trial and Hearing Deadlines:
    description: Deadlines concerning trial and hearing preparation, including witness lists, exhibit lists, and stipulations.
    rules:
      trial_001:
        rule: "Rule 1.201(b)(3)"
        deadline: "no later than 2 months"
        description: "The court must arrange for a sufficient number of available jurors."
        trigger: "before the date scheduled for jury selection"
        consequence: "Potential delay or cancellation of trial."
        extensions: "Not specified."
        trigger_event: Trial Scheduling
        trigger_parameters:
          original_text: "before the date scheduled for jury selection"
          jurisdiction: Florida
```
```yaml
jurisdiction: Florida
court_type: Civil
practice_area: General Civil Litigation
last_updated: '2025-06-13'
source: Florida Rules of Civil Procedure
extraction_method: Gemini 2.0 Flash Enhanced Analysis
extraction_date: '2025-06-13T12:00:00'
total_deadlines_extracted: 206
deadline_categories:
  Pleading and Response Deadlines:
    description: Deadlines related to filing pleadings and responses.
    rules:
      pleading_1:
        rule: 1.320(a)
        deadline: Within 30 days
        description: Serve cross questions on all other parties.
        trigger: After the notice and written questions are served.
        consequence: N/A
        extensions: The court may for cause shown enlarge or shorten the time.
        trigger_event: Service of Notice
        trigger_parameters:
          original_text: After the notice and written questions are served
          jurisdiction: Florida
      pleading_2:
        rule: 1.320(a)
        deadline: Within 10 days
        description: Serve recross questions on all other parties.
        trigger: After being served with redirect questions.
        consequence: N/A
        extensions: The court may for cause shown enlarge or shorten the time.
        trigger_event: Service of Redirect Questions
        trigger_parameters:
          original_text: After being served with redirect questions
          jurisdiction: Florida
      pleading_3:
        rule: 1.320(a)
        deadline: Within 10 days
        description: Serve objections to the form of written questions in writing on the party propounding them.
        trigger: After service of the last questions authorized.
        consequence: Objections are waived.
        extensions: The court may for cause shown enlarge or shorten the time.
        trigger_event: Service of Last Questions
        trigger_parameters:
          original_text: after service of the last questions authorized
          jurisdiction: Florida
      pleading_4:
        rule: 1.340(a)(9)
        deadline: Within 30 days
        description: Serve the answers and any objections to interrogatories.
        trigger: After the service of the interrogatories.
        consequence: N/A
        extensions: The court may allow a shorter or longer time.
        trigger_event: Service of Interrogatories
        trigger_parameters:
          original_text: after the service of the interrogatories
          jurisdiction: Florida
      pleading_5:
        rule: 1.340(a)(9)
        deadline: Within 45 days
        description: Defendant may serve answers or objections to interrogatories.
        trigger: After service of the process and initial pleading on that defendant.
        consequence: N/A
        extensions: The court may allow a shorter or longer time.
        trigger_event: Service of Process
        trigger_parameters:
          original_text: after service of the process and initial pleading on that defendant
          jurisdiction: Florida
      pleading_6:
        rule: 1.350(b)(3)
        deadline: Within 30 days
        description: The party to whom the request is directed must serve a written response.
        trigger: After service of the request.
        consequence: N/A
        extensions: The court may allow a shorter or longer time.
        trigger_event: Service of Request
        trigger_parameters:
          original_text: after service of the request
          jurisdiction: Florida
      pleading_7:
        rule: 1.350(b)(3)
        deadline: Within 45 days
        description: Defendant may serve a response.
        trigger: After service of the process and initial pleading on that defendant.
        consequence: N/A
        extensions: The court may allow a shorter or longer time.
        trigger_event: Service of Process
        trigger_parameters:
          original_text: after service of the process and initial pleading on that defendant
          jurisdiction: Florida
      pleading_8:
        rule: 1.351(b)
        deadline: 10 days
        description: Wait to issue a subpoena after notice by delivery or email service on every other party of the intent to serve a subpoena.
        trigger: N/A
        consequence: Premature production of documents by nonparties
        extensions: N/A
        trigger_event: Notice of intent to serve subpoena
        trigger_parameters:
          original_text: until 10 days after notice by delivery or e -mail service
          jurisdiction: Florida
      pleading_9:
        rule: 1.351(b)
        deadline: 15 days
        description: Wait to issue a subpoena after notice by mail service on every other party of the intent to serve a subpoena.
        trigger: N/A
        consequence: Premature production of documents by nonparties
        extensions: N/A
        trigger_event: Notice of intent to serve subpoena
        trigger_parameters:
          original_text: 15 days after notice by mail service
          jurisdiction: Florida
      pleading_10:
        rule: 1.351(b)
        deadline: 45 days
        description: The subpoena shall not issue earlier than 45 days after service on the last -served party, if the notice is served with original process
        trigger: Service with original process
        consequence: N/A
        extensions: N/A
        trigger_event: Service of original process
        trigger_parameters:
          original_text: the subpoena shall not issue earlier than 45 days after service on the last -served party
          jurisdiction: Florida
      pleading_11:
        rule: 1.351(b)
        deadline: Within 10 days
        description: Any party serves an objection to production under this rule within 10 days of service by delivery or e-mail of the notice.
        trigger: Service by delivery or email of the notice
        consequence: the documents or things shall not be produced pending resolution of the objection
        extensions: N/A
        trigger_event: Service of notice
        trigger_parameters:
          original_text: within 10 days of service by delivery or e-mail of the notice
          jurisdiction: Florida
      pleading_12:
        rule: 1.351(b)
        deadline: Within 15 days
        description: Any party serves an objection to production under this rule within 15 days if service by U.S. mail.
        trigger: Service by U.S. mail of the notice
        consequence: the documents or things shall not be produced pending resolution of the objection
        extensions: N/A
        trigger_event: Service of notice
        trigger_parameters:
          original_text: (15 days if service by U.S. mail)
          jurisdiction: Florida
      pleading_13:
        rule: 1.351(b)
        deadline: Within 45 days
        description: Any party serves an objection to production under this rule within 45 days of service of process if the notice is served with original process.
        trigger: Service of process if the notice is served with original process
        consequence: the documents or things shall not be produced pending resolution of the objection
        extensions: N/A
        trigger_event: Service of notice
        trigger_parameters:
          original_text: or within 45 days of service of process if the notice is served with original process
          jurisdiction: Florida
      pleading_14:
        rule: 1.360(a)(1)(A)
        deadline: Within 30 days
        description: The party to whom the request is directed shall serve a response.
        trigger: After service of the request.
        consequence: N/A
        extensions: The court may allow a shorter or longer time.
        trigger_event: Service of Request
        trigger_parameters:
          original_text: After service of the request
          jurisdiction: Florida
      pleading_15:
        rule: 1.360(a)(1)(A)
        deadline: Within 45 days
        description: A defendant need not serve a response
        trigger: until 45 days after service of the process and initial pleading on that defendant
        consequence: N/A
        extensions: The court may allow a shorter or longer time.
        trigger_event: Service of process
        trigger_parameters:
          original_text: until 45 days after service of the process and initial pleading on that defendant
          jurisdiction: Florida
      pleading_16:
        rule: 1.370(a)
        deadline: Within 30 days
        description: Serve upon the party requesting the admission a written answer or objection addressed to the matter.
        trigger: After service of the request.
        consequence: The matter is admitted.
        extensions: Such shorter or longer time as the court may allow.
        trigger_event: Service of Request
        trigger_parameters:
          original_text: within 30 days after service of the request
          jurisdiction: Florida
      pleading_17:
        rule: 1.370(a)
        deadline: Before the expiration of 45 days
        description: Defendant shall not be required to serve answers or objections.
        trigger: After service of the process and initial pleading upon the defendant.
        consequence: N/A
        extensions: Unless the court shortens the time.
        trigger_event: Service of Process
        trigger_parameters:
          original_text: before the expiration of 45 days after service of the process and initial pleading upon the defendant
          jurisdiction: Florida
      pleading_18:
        rule: 1.410(e)(1)
        deadline: Within 10 days
        description: The person to whom the subpoena is directed may serve a written objection to inspecting or copying of any of the designated materials.
        trigger: After its service.
        consequence: The party serving the subpoena will not be entitled to inspect and copy the materials except under an order of the court.
        extensions: N/A
        trigger_event: Service of Subpoena
        trigger_parameters:
          original_text: Within 10 days after its service
          jurisdiction: Florida
      pleading_19:
        rule: 1.410(e)(1)
        deadline: On or before the time specified in the subpoena for compliance.
        description: The person to whom the subpoena is directed may serve a written objection to inspecting or copying of any of the designated materials.
        trigger: If the time is less than 10 days after service
        consequence: The party serving the subpoena will not be entitled to inspect and copy the materials except under an order of the court.
        extensions: N/A
        trigger_event: Service of Subpoena
        trigger_parameters:
          original_text: on or before the time specified in the subpoena for compliance if the time is less than 10 days after service
          jurisdiction: Florida
  Motion Practice Deadlines:
    description: Deadlines associated with motion practice.
    rules:
      motion_1:
        rule: 1.431(h)
        deadline: Within 15 days
        description: Serve a motion for an order permitting an interview of a juror or jurors.
        trigger: After rendition of the verdict.
        consequence: N/A
        extensions: Unless good cause is shown for the failure to make the motion within that time.
        trigger_event: Verdict Rendered
        trigger_parameters:
          original_text: within 15 days after rendition of the verdict
          jurisdiction: Florida
  Trial and Hearing Deadlines:
    description: Deadlines related to trial and hearings.
    rules:
      trial_1:
        rule: 1.430(b)
        deadline: Not later than 10 days
        description: Serve a demand for a trial by jury.
        trigger: After the service of the last pleading directed to such issue.
        consequence: Waiver of trial by jury.
        extensions: Court may allow an amendment in the proceedings to demand a trial by jury or order a trial by jury on its own motion.
        trigger_event: Service of Last Pleading
        trigger_parameters:
          original_text: not later than 10 days after the service of the last pleading directed to such issue
          jurisdiction: Florida
      trial_2:
        rule: 1.430(c)
        deadline: 10 days
        description: Serve a demand for trial by jury of any other or all of the issues triable by jury.
        trigger: after service of the demand
        consequence: N/A
        extensions: such lesser time as the court may order
        trigger_event: Service of demand
        trigger_parameters:
          original_text: 10 days after service of the demand
          jurisdiction: Florida
      trial_3:
        rule: 1.430(d)
        deadline: Within 60 days
        description: written stipulation and a written motion requesting authorization must be filed with the court
        trigger: After service of a demand under subdivision (b)
        consequence: N/A
        extensions: within such other period as may be directed by the court
        trigger_event: Service of demand
        trigger_parameters:
          original_text: within 60 days after service of a demand under subdivision (b) or within such other period as may be directed by the court
          jurisdiction: Florida
  Case Management Deadlines:
    description: Deadlines related to case management.
    rules:
      case_management_1:
        rule: 1.420(e)
        deadline: 10 months
        description: An interested person, the court, or the clerk of the court may serve notice to all parties that no such activity has occurred.
        trigger: No activity by filing of pleadings, order of court, or otherwise has occurred for a period of 10 months, and no order staying the action has been issued nor stipulation for stay approved by the court
        consequence: Dismissal of action.
        extensions: N/A
        trigger_event: Inactivity
        trigger_parameters:
          original_text: that no activity by filing of pleadings, order of court, or otherwise has occurred for a period of 10 months
          jurisdiction: Florida
      case_management_2:
        rule: 1.420(e)
        deadline: 60 days
        description: The action shall be dismissed by the court on its own motion or on the motion of any interested person, after reasonable notice to the parties.
        trigger: if no such record activity has occurred within the 10 months immediately preceding the service of such notice, and no record activity occurs within the 60 days immediately following the service of such notice, and if no stay was issued or approved prior to the expiration of such 60 -day period
        consequence: action shall be dismissed
        extensions: Unless a party shows good cause in writing at least 5 days before the hearing on the motion why the action should remain pending.
        trigger_event: Inactivity and Notice
        trigger_parameters:
          original_text: no record activity occurs within the 60 days immediately following the service of such notice
          jurisdiction: Florida
      case_management_3:
        rule: 1.420(e)
        deadline: at least 5 days
        description: Shows good cause in writing why the action should remain pending.
        trigger: before the hearing on the motion why the action should remain pending
        consequence: N/A
        extensions: N/A
        trigger_event: motion hearing
        trigger_parameters:
          original_text: at least 5 days before the hearing on the motion
          jurisdiction: Florida
      case_management_4:
        rule: 1.420(e)
        deadline: 1 year
        description: Mere inaction for a period of less than 1 year shall not be sufficient cause for dismissal for failure to prosecute.
        trigger: N/A
        consequence: N/A
        extensions: N/A
        trigger_event: Inactivity
        trigger_parameters:
          original_text: Mere inaction for a period of less than 1 year shall not be sufficient cause for dismissal for failure to prosecute
          jurisdiction: Florida
      case_management_5:
        rule: 1.440(c)(2)
        deadline: Not later than 45 days
        description: the court must enter an order setting the trial period
        trigger: before the projected trial period set forth in the case management order
        consequence: N/A
        extensions: N/A
        trigger_event: Trial Period
        trigger_parameters:
          original_text: not later than 45 days before the projected trial period set forth in the case management order
          jurisdiction: Florida
      case_management_6:
        rule: 1.440(c)(4)
        deadline: at least 30 days
        description: Any order setting a trial period must set the trial period to begin.
        trigger: after the date of the court’s service of the order
        consequence: N/A
        extensions: unless all parties agree otherwise
        trigger_event: Service of Order
        trigger_parameters:
          original_text: at least 30 days after the date of the court’s service of the order
          jurisdiction: Florida
  Settlement Deadlines:
    description: Deadlines relating to settlement proposals.
    rules:
      settlement_1:
        rule: 1.442(b)
        deadline: No earlier than 90 days
        description: Serve a proposal to a defendant.
        trigger: After service of process on that defendant.
        consequence: N/A
        extensions: N/A
        trigger_event: Service of Process
        trigger_parameters:
          original_text: no earlier than 90 days after service of process on that defendant
          jurisdiction: Florida
      settlement_2:
        rule: 1.442(b)
        deadline: No earlier than 90 days
        description: Serve a proposal to a plaintiff.
        trigger: After the action has been commenced.
        consequence: N/A
        extensions: N/A
        trigger_event: Commencement of Action
        trigger_parameters:
          original_text: no earlier than 90 days after the action has been commenced
          jurisdiction: Florida
      settlement_3:
        rule: 1.442(b)
        deadline: No later than 45 days
        description: Serve a proposal.
        trigger: Before the date set for trial or the first day of the docket on which the case is set for trial, whichever is earlier.
        consequence: N/A
        extensions: N/A
        trigger_event: Trial Date
        trigger_parameters:
          original_text: no later than 45 days before the date set for trial or the first day of the docket on which the case is set for trial, whichever is earlier
          jurisdiction: Florida
      settlement_4:
        rule: 1.442(f)(1)
        deadline: Within 30 days
        description: Accept a proposal by delivery of a written notice of acceptance.
        trigger: After service of the proposal.
        consequence: Proposal is deemed rejected.
        extensions: In any case in which the existence of a class is alleged, the time for acceptance of a proposal for settlement is extended to 30 days after the date the order granting or denying certification is filed.
        trigger_event: Service of Proposal
        trigger_parameters:
          original_text: within 30 days after service of the proposal
          jurisdiction: Florida
      settlement_5:
          rule: 1.442(f)(2)
          deadline: 30 days
          description: the time for acceptance of a proposal for settlement is extended
          trigger: after the date the order granting or denying certification is filed
          consequence: N/A
          extensions: N/A
          trigger_event: court order
          trigger_parameters:
            original_text: is extended to 30 days after the date the order granting or denying certification is filed
            jurisdiction: Florida
  Discovery Deadlines:
    description: Deadlines related to discovery procedures
    rules:
      discovery_1:
        rule: 1.410(e)(1)
        deadline: Within  10  days
        description: Serve a written objection to inspecting or copying any of the designated materials.
        trigger: After service of the subpoena.
        consequence: The party serving the subpoena will not be entitled to inspect and copy the materials except under an order of the court from which the subpoena was issued.
        extensions: N/A
        trigger_event: Service of Subpoena
        trigger_parameters:
          original_text: Within 10 days after its service
          jurisdiction: Florida
      discovery_2:
        rule: 1.431(c)(1)
        deadline: within 30 days
        description: employee or has been an employee of any party or any other person or entity against whom liability or blame is alleged in the pleadings
        trigger: before the trial.
        consequence: a ground of challenge for cause
        extensions: N/A
        trigger_event: Trial
        trigger_parameters:
          original_text: within 30 days before the trial
          jurisdiction: Florida
```
```yaml
jurisdiction: Florida
court_type: Civil
practice_area: General Civil Litigation
last_updated: '2025-06-13'
source: Florida Rules of Civil Procedure
extraction_method: Gemini 2.0 Flash Enhanced Analysis
extraction_date: '2025-06-13T12:00:00'
total_deadlines_extracted: 207
deadline_categories:
  Pleading and Response Deadlines:
    description: Deadlines related to filing and responding to pleadings
    rules:
      rule_1_491b1A:
        rule: 1.491(b)(1)(A)
        deadline: within 10 days
        description: File a written objection to the referral to a magistrate handling residential mortgage foreclosures.
        trigger: Service of the order of referral or within the time to respond to the initial pleading, whichever is later.
        consequence: Failure to file a written objection is deemed to be consent to the order of referral.
        extensions: N/A
        trigger_event: Service of order
        trigger_parameters:
          original_text: within 10 days of the service of the order of referral or within the time to respond to the initial pleading, whichever is later
          jurisdiction: Florida
      rule_1_491b1B:
        rule: 1.491(b)(1)(B)
        deadline: before commencement of the hearing
        description: File a written objection to the referral to a magistrate handling residential mortgage foreclosures
        trigger: Time set for hearing is less than 10 days after service of the order of referral
        consequence: Failure to file a written objection is deemed to be consent to the order of referral.
        extensions: N/A
        trigger_event: Hearing scheduled
        trigger_parameters:
          original_text: If the time set for the hearing is less than 10 days after service of the order of referral, the objection must be filed before commencement of the hearing
          jurisdiction: Florida
      rule_1_625:
        rule: 1.625
        deadline: within 20 days
        description: Serve a response to the motion affecting the surety's liability on a judicial bond
        trigger: Service of the motion.
        consequence: A default may be taken.
        extensions: N/A
        trigger_event: Motion served
        trigger_parameters:
          original_text: The surety must serve a response to the motion within 20 days after service of the motion, asserting any defenses in law or in fact.
          jurisdiction: Florida

  Discovery Deadlines:
    description: Deadlines related to discovery procedures, including document production, depositions, and interrogatories.
    rules:
      rule_1_560b:
        rule: 1.560(b)
        deadline: within 45 days
        description: Complete form 1.977, including all required attachments.
        trigger: Order of the court or other reasonable time as determined by the court.
        consequence: Failure to obey the order may be considered contempt of court.
        extensions: Other reasonable time as determined by the court
        trigger_event: Court Order
        trigger_parameters:
          original_text: complete form 1.977, including all required attachments, within 45 days of the order or other reasonable time as determined by the court
          jurisdiction: Florida
      rule_1_560c:
        rule: 1.560(c)
        deadline: within 45 days
        description: Complete under oath Florida Rule of Civil Procedure Form 1.977 (Fact Information Sheet), including all required attachments, and serve it on the judgment creditor’s attorney, or the judgment creditor if the judgment creditor is not represented by an attorney.
        trigger: Date of final judgment, unless the final judgment is satisfied or post- judgment discovery is stayed.
        consequence: Jurisdiction of this case is retained to enter further orders that are proper to compel the judgment debtor(s) to complete form 1.977, including all required attachments, and serve it on the judgment creditor’s attorney, or the judgment creditor if the judgment creditor is not represented by an attorney.
        extensions: Stay of Post-Judgment Discovery
        trigger_event: Final Judgment
        trigger_parameters:
          original_text:  within 45 days from the date of this final judgment, unless the final judgment is satisfied or post- judgment discovery is stayed.
          jurisdiction: Florida
      rule_1_650c2B:
        rule: 1.650(c)(2)(B)
        deadline: within 20 days
        description: Produce discoverable documents or things at the expense of the requesting party.
        trigger: Serving the request.
        consequence: Failure of a party to comply with the above time limits must not relieve that party of its obligation under the statute but must be evidence of failure of that party to comply with the good faith requirements of section 766.106, Florida Statutes.
        extensions: N/A
        trigger_event: Discovery request served
        trigger_parameters:
          original_text: The documents or things must be produced at the expense of the requesting party within 20 days of serving the request.
          jurisdiction: Florida
      rule_1_650c2D:
        rule: 1.650(c)(2)(D)
        deadline: within 20 days
        description: Respond to written questions, the number of which may not exceed 30, including subparts
        trigger: Service of the questions.
        consequence: Failure of a party to comply with the above time limits w ill not relieve that party of its obligation under the statute, but will be evidence of failure of that party to comply with the good faith requirements of section 766.106, Florida Statutes.
        extensions: N/A
        trigger_event: Interrogatories served
        trigger_parameters:
          original_text: The party to whom the written questions are directed must respond within 20 days of service of the questions.
          jurisdiction: Florida
  Motion Practice Deadlines:
    description: Deadlines related to filing, serving, and responding to motions.
    rules:
      rule_1_480b:
        rule: 1.480(b)
        deadline: Within 15 days
        description: Serve a motion to set aside the verdict and any judgment entered thereon and to enter judgment in accordance with the motion for a directed verdict.
        trigger: After the return of a verdict, a party who has timely moved for a directed verdict.
        consequence: N/A
        extensions: N/A
        trigger_event: Return of Verdict
        trigger_parameters:
          original_text: Within 15 days after the return of a verdict, a party who has timely moved for a directed verdict may serve a motion to set aside the verdict and any judgment entered thereon and to enter judgment in accordance with the motion for a directed verdict.
          jurisdiction: Florida
      rule_1_480b_no_verdict:
        rule: 1.480(b)
        deadline: Within 15 days
        description: Serve a motion for judgment in accordance with the motion for a directed verdict.
        trigger: After discharge of the jury, where a verdict was not returned, a party who has timely moved for a directed verdict.
        consequence: N/A
        extensions: N/A
        trigger_event: Jury Discharge
        trigger_parameters:
          original_text: a party who has timely moved for a directed verdict may serve a motion for judgment in accordance with the motion for a directed verdict within 15 days after discharge of the jury.
          jurisdiction: Florida
      rule_1_525:
        rule: 1.525
        deadline: no later than 30 days
        description: Serve a motion seeking a judgment taxing costs, attorneys’ fees, or both.
        trigger: Filing of the judgment, including a judgment of dismissal, or the service of a notice of voluntary dismissal, which judgment or notice concludes the action as to that party.
        consequence: N/A
        extensions: N/A
        trigger_event: Conclusion of action
        trigger_parameters:
          original_text: Any party seeking a judgment taxing costs, attorneys’ fees, or both shall serve a motion no later than 30 days after filing of the judgment, including a judgment of dismissal, or the service of a notice of voluntary dismissal, which judgment or notice concludes the action as to that party.
          jurisdiction: Florida
      rule_1_530b:
        rule: 1.530(b)
        deadline: not later than 15 days
        description: Serve a motion for new trial or for rehearing.
        trigger: Return of the verdict in a jury action or the date of filing of the judgment in a non-jury action.
        consequence: N/A
        extensions: Amended to state new grounds at any time before the motion is determined.
        trigger_event: Verdict or Judgment
        trigger_parameters:
          original_text: A motion for new trial or for rehearing must be served not later than 15 days after the return of the verdict in a jury action or the date of filing of the judgment in a non -jury action.
          jurisdiction: Florida
      rule_1_530c:
        rule: 1.530(c)
        deadline: 10 days
        description: Serve opposing affidavits
        trigger: service of motion for a new trial based on affidavits
        consequence: N/A
        extensions: "Additional period not exceeding 20 days either by the court for good cause shown or by the parties by written stipulation"
        trigger_event: Service of motion
        trigger_parameters:
          original_text: The opposing party has 10 days after such service within which to serve opposing affidavits, which period may be extended for an additional period not exceeding 20 days either by the court for good cause shown or by the parties by written stipulation.
          jurisdiction: Florida
      rule_1_530d:
        rule: 1.530(d)
        deadline: Not later than 15 days
        description: Court of its own initiative may order a rehearing or a new trial
        trigger: After the date of filing of the judgment or within the time of ruling on a timely motion for a rehearing or a new trial made by a party.
        consequence: N/A
        extensions: N/A
        trigger_event: Judgment entered
        trigger_parameters:
          original_text: Not later than 15 days after the date of filing of the judgment or within the time of ruling on a timely motion for a rehearing or a new trial made by a party, the court of its own initiative may order a rehearing or a new trial for any reason for which it might have granted a rehearing or a new trial on moti on of a party.
          jurisdiction: Florida
      rule_1_530g:
        rule: 1.530(g)
        deadline: not later than 15 days
        description: Serve a motion to alter or amend a judgment
        trigger: Date of filing of the judgment
        consequence: N/A
        extensions: N/A, except for remedies in rule 1.540(b)
        trigger_event: Judgment entered
        trigger_parameters:
          original_text: A motion to alter or amend the judgment shall be served not later than 15 days after the date of filing of the judgment, except that this rule does not affect the remedies in rule 1.540(b).
          jurisdiction: Florida
      rule_1_530h1:
        rule: 1.530(h)(1)
        deadline: Not later than 15 days
        description: Serve a motion for remittitur or additur.
        trigger: Return of the verdict in a jury action or the date of filing of the judgment in a non-jury action.
        consequence: N/A
        extensions: N/A
        trigger_event: Verdict or Judgment
        trigger_parameters:
          original_text: Not later than 15 days after the return of the verdict in a jury action or the date of filing of the judgment in a non -jury action, any party may serve a motion for remittitur or additur.
          jurisdiction: Florida
      rule_1_530h3:
        rule: 1.530(h)(3)
        deadline: within 15 days
        description: Filing a written election for a new trial on the issue of damages only
        trigger: After the order granting remittitur or additur is filed
        consequence: N/A
        extensions: N/A
        trigger_event: Order Granting Remittitur/Additur
        trigger_parameters:
          original_text: Any party adversely affected by the order granting remittitur or additur may reject the award and elect a new trial on the issue of damages only by filing a written election within 15 days after the order granting remittitur or additur is filed.
          jurisdiction: Florida
      rule_1_700b:
        rule: 1.700(b)
        deadline: within 15 days
        description: Move to dispense with mediation or arbitration
        trigger: order of referral
        consequence: N/A
        extensions: N/A
        trigger_event: Referral Order
        trigger_parameters:
          original_text: A party may move, within 15 days after the order of referral, to dispense with mediation or arbitration
          jurisdiction: Florida
      rule_1_700c:
        rule: 1.700(c)
        deadline: Within 15 days
        description: File a motion to defer the mediation or arbitration proceeding
        trigger: Order of Referral
        consequence: Mediation or arbitration shall be tolled until disposition of the motion.
        extensions: N/A
        trigger_event: Referral Order
        trigger_parameters:
          original_text: Within 15 days of the order of referral, any party may file a motion with the court to defer the proceeding.
          jurisdiction: Florida
      rule_1_720j1:
        rule: 1.720(j)(1)
        deadline: Within 10 days
        description: Parties may agree upon a stipulation with the court designating a certified mediator
        trigger: Order of Referral
        consequence: If parties cannot agree, the court shall appoint a certified mediator
        extensions: N/A
        trigger_event: Referral Order
        trigger_parameters:
          original_text: Within 10 days of the order of referral, the parties may agree upon a stipulation with the court designating
          jurisdiction: Florida
      rule_1_720j2:
        rule: 1.720(j)(2)
        deadline: Within 10 days
        description: Plaintiff or petitioner shall notify the court that the parties cannot agree upon a mediator.
        trigger: 10 days of the expiration of the period to agree on a mediator
        consequence: The court shall appoint a certified mediator selected by rotation or by such other procedures as may be adopted by administrative order of the chief judge.
        extensions: N/A
        trigger_event: Mediation Designation
        trigger_parameters:
          original_text: If the parties cannot agree upon a mediator within 10 days of the order of referral, the plaintiff or petitioner shall so notify the court within 10 days of the expiration of the period to agree on a mediator
          jurisdiction: Florida
      rule_1_720k:
        rule: 1.720(k)
        deadline: Within 15 days
        description: Parties may object to the rate of the mediator’s compensation
        trigger: Order of Referral
        consequence: N/A
        extensions: N/A
        trigger_event: Referral Order
        trigger_parameters:
          original_text: Parties may object to the rate of the mediator’s compensation within 15 days of the order of referral by serving an objection on all other parties and the mediator.
          jurisdiction: Florida

  Trial and Hearing Deadlines:
    description: Deadlines related to trial preparation, conduct, and post-trial motions.
    rules:
      rule_1_470b:
        rule: 1.470(b)
        deadline: Not later than at the close of the evidence
        description: Parties shall file written requests that the court instruct the jury on the law set forth in such requests.
        trigger: Close of the evidence
        consequence: No party may assign as error the giving of any instruction unless that party objects thereto at such time, or the failure to give any instruction unless that party requested the same.
        extensions: N/A
        trigger_event: Close of Evidence
        trigger_parameters:
          original_text: Not later than at the close of the evidence, the parties shall file written requests that the court instruct the jury on the law set forth in such requests.
          jurisdiction: Florida
      rule_1_610d:
        rule: 1.610(d)
        deadline: within 5 days
        description: Motion to dissolve or modify an injunction shall be heard.
        trigger: After movant applies for a hearing on the motion to dissolve or modify a temporary injunction.
        consequence: N/A
        extensions: N/A
        trigger_event: Motion hearing requested
        trigger_parameters:
          original_text: If a party moves to dissolve or modify, the motion shall be heard within 5 days after the movant applies for a hearing on the motion.
          jurisdiction: Florida

  Case Management Deadlines:
    description: Deadlines related to case management, including scheduling and conferences.
    rules:
      rule_1_700a1:
        rule: 1.700(a)(1)
        deadline: within 60 days
        description: The first mediation conference or arbitration hearing must be held.
        trigger: Order of referral.
        consequence: N/A
        extensions: Unless otherwise ordered by the court.
        trigger_event: Referral Order
        trigger_parameters:
          original_text: Unless otherwise ordered by the court, the first mediation conference or arbitration hearing must be held within 60 days of the order of referral.
          jurisdiction: Florida
      rule_1_700a2:
        rule: 1.700(a)(2)
        deadline: Within 15 days
        description: The court or its designee, who may be the mediator or the chief arbitrator, must notify the parties in writing of the date, the time, and, as applicable, the place of the conference or hearing and the instructions for access to communication technology that will be used for the conference or hearing.
        trigger: After the designation of the mediator or the arbitrator.
        consequence: N/A
        extensions: N/A
        trigger_event: Mediation Designation
        trigger_parameters:
          original_text: Within 15 days after the designation of the mediator or the arbitrator, the court or its designee, who may be the mediator or the chief arbitrator, must notify the parties in writing of the date, the time, and , as applicable, the place of the conference or hearing and the instructions for access to communication technology that will be used for the conference or hearing
          jurisdiction: Florida
      rule_1_710a:
        rule: 1.710(a)
        deadline: within 45 days
        description: Mediation shall be completed.
        trigger: First mediation conference.
        consequence: N/A
        extensions: Extended by order of the court or by stipulation of the parties.
        trigger_event: Mediation Conference
        trigger_parameters:
          original_text: Mediation shall be completed within 45 days of the first mediation conference unless extended by order of the court or by stipulation of the parties.
          jurisdiction: Florida
      rule_1_720e:
        rule: 1.720(e)
        deadline: 10 days prior
        description: Each party must file with the court and serve all parties a written notice identifying the person or persons who will appear at the mediation conference and confirming their authority.
        trigger: Appearing at a mediation conference.
        consequence: The failure to file a confirmation of authority required under subdivision (e) above, or failure of the persons actually identified in the confirmation to appear at the mediation conference, shall create a rebuttable presumption of a failure to appear.
        extensions: Unless otherwise stipulated by the parties
        trigger_event: Mediation Conference
        trigger_parameters:
          original_text:  each party, 10 days prior to appearing at a mediation conference, must file with the court and serve all parties a written notice identifying the person or persons who will appear at the mediation conference as a party representative or as an insurance carrier representative, and confirming that those persons have the authority required by subdivision (b).
          jurisdiction: Florida
      rule_1_810a:
        rule: 1.810(a)
        deadline: within 15 days
        description: The court shall determine the number of arbitrators and designate them.
        trigger: After service of the order of referral in the absence of an agreement by the parties
        consequence: N/A
        extensions: N/A
        trigger_event: Referral Order
        trigger_parameters:
          original_text: The court shall determine the number of arbitrators and designate them within 15 days after service of the order of referral in the absence of an agreement by the parties.
          jurisdiction: Florida
      rule_1_820g1:
        rule: 1.820(g)(1)
        deadline: Within 30 days
        description: Arbitration must be completed
        trigger: First arbitration hearing
        consequence: N/A
        extensions: Extensions of time must not exceed 60 days from the date of the first arbitration hearing, by order of the court on motion of the chief arbitrator or a party
        trigger_event: Hearing commences
        trigger_parameters:
          original_text: Arbitration must be completed within 30 days of the first arbitration hearing unless extended by order of the court on motion of the chief arbitrator or of a party. E xtension s of time must not exceed 60 days from the date of the first arbitration hearing.
          jurisdiction: Florida
      rule_1_820g3:
        rule: 1.820(g)(3)
        deadline: Within 10 days
        description: The arbitrator(s) shall notify the parties, in writing, of their decision.
        trigger: Final adjournment of the arbitration hearing.
        consequence: N/A
        extensions: N/A
        trigger_event: Hearing adjourns
        trigger_parameters:
          original_text: Within 10 days of the final adjournment of the arbitration hearing, the arbitrator(s) shall notify the parties, in writing, of their decision.
          jurisdiction: Florida
      rule_1_820h:
        rule: 1.820(h)
        deadline: Within 20 days
        description: File a notice of rejection of the arbitration decision and request for trial.
        trigger: Service of the arbitrator(’s)(s’) written decision
        consequence: N/A
        extensions: If a notice of rejection of the arbitration decision and request for trial is filed by any party, any party having a third -party claim at issue at the time of arbitration may file a notice of rejection of the arbitration decision and request for trial within 10 days of service of the first notice of rejection of the arbitration decision and request for trial
        trigger_event: Arbitration Decision
        trigger_parameters:
          original_text: To reject the arbitration decision, within 20 days of service of the arbitrator(’s)(s’) written decision, a ny party m ust file a notice of rejection of the arbitration decision and request for trial in the same document.
          jurisdiction: Florida
      rule_1_820h_third_party:
        rule: 1.820(h)
        deadline: within 10 days
        description: any party having a third -party claim at issue at the time of arbitration may file a notice of rejection of the arbitration decision and request for trial
        trigger: Service of the first notice of rejection of the arbitration decision and request for trial
        consequence: N/A
        extensions: N/A
        trigger_event: Arbitration Rejection Notice served
        trigger_parameters:
          original_text: any party having a third -party claim at issue at the time of arbitration may file a notice of rejection of the arbitration decision and request for trial within 10 days of service of the first notice of rejection of the arbitration decision and request for trial
          jurisdiction: Florida

  Appeal and Post-Trial Deadlines:
    description: Deadlines related to post-trial motions and appeals.

  Service and Notice Deadlines:
    description: Deadlines related to service and notice requirements.

  Emergency and Expedited Deadlines:
    description: Deadlines related to expedited or emergency procedures.
    rules:
      rule_1_610b:
        rule: 1.610(b)
        deadline: within 5 days
        description: Bond shall be posted
        trigger: Entry of the order setting the bond
        consequence: N/A
        extensions: Unless otherwise specified by the court
        trigger_event: Court Order
        trigger_parameters:
          original_text: Unless otherwise specified by the court, the bond shall be posted within 5 days of entry of the order setting the bond.
          jurisdiction: Florida

  Summary Judgment Deadlines:
    description: Deadlines specifically for Summary Judgement.
    rules:
      rule_1_510b:
        rule: 1.510(b)
        deadline: after the expiration of 20 days
        description: A party may move for summary judgment.
        trigger: From the commencement of the action or after service of a motion for summary judgment by the adverse party.
        consequence: N/A
        extensions: N/A
        trigger_event: Case commencement
        trigger_parameters:
          original_text: A party may move for summary judgment at any time after the expiration of 20 days from the commencement of the action or after service of a motion for summary judgment by the adverse party.
          jurisdiction: Florida
      rule_1_510c5_movant:
        rule: 1.510(c)(5)
        deadline: At the time of filing a motion for summary judgment
        description: the movant must also serve the movant’s supporting factual position as provided in subdivision (1) above.
        trigger: filing a motion for summary judgment
        consequence: N/A
        extensions: N/A
        trigger_event: Filing motion for summary judgement
        trigger_parameters:
          original_text: At the time of filing a motion for summary judgment, the movant must also serve the movant’s supporting factual position as provided in subdivision (1) above.
          jurisdiction: Florida
      rule_1_510c5_nonmovant:
        rule: 1.510(c)(5)
        deadline: No later than 40 days
        description:  the nonmovant must serve a response that includes the nonmovant’s supporting factual position as provided in subdivision (1) above.
        trigger: after service of the motion for summary judgment
        consequence: N/A
        extensions: N/A
        trigger_event: Service of motion for summary judgement
        trigger_parameters:
          original_text: No later than 40 days after service of the motion for summary judgment, the nonmovant must serve a response that includes the nonmovant’s supporting factual position as provided in subdivision (1) above.
          jurisdiction: Florida
      rule_1_510c6:
        rule: 1.510(c)(6)
        deadline: at least 10 days
        description: hearing on a motion for summary judgment must be set for a date
        trigger: after the deadline for serving a response
        consequence: N/A
        extensions: unless the parties stipulate or the court orders otherwise
        trigger_event: Response deadline
        trigger_parameters:
          original_text: Any hearing on a motion for summary judgment must be set for a date at least 10 days after the deadline for serving a response, unless the parties stipulate or the court orders otherwise.
          jurisdiction: Florida
      rule_1_540b:
        rule: 1.540(b)
        deadline: not more than 1 year
        description: Motion shall be filed for mistake, inadvertence, surprise, or excusable neglect, newly discovered evidence, fraud, etc.
        trigger: the judgment, decree, order, or proceeding was entered or taken.
        consequence: N/A
        extensions: This rule does not limit the power of a court to entertain an independent action to relieve a party from a judgment, decree, order, or proceeding or to set aside a judgment, decree, or order for fraud upon the court.
        trigger_event: Judgment, Decree, or Order
        trigger_parameters:
          original_text: The motion shall be filed within a reasonable time, and for reasons (1), (2), and (3) not more than 1 year after the judgment, decree, order, or proceeding was entered or taken.
          jurisdiction: Florida

  Receivership deadlines:
    description: Deadlines specifically for receivership actions.
    rules:
      rule_1_620b_inventory:
        rule: 1.620(b)
        deadline: within 20 days
        description: Every receiver shall file in the clerk’s office a true and complete inventory under oath of the property coming under the receiver’s control or possession under the receiver’s appointment
        trigger: Appointment of receiver
        consequence: N/A
        extensions: N/A
        trigger_event: Receivership Appointment
        trigger_parameters:
          original_text: Every receiver shall file in the clerk’s office a true and complete inventory under oath of the property coming under the receiver’s control or possession under the receiver’s appointment within 20 days after appointment.
          jurisdiction: Florida
      rule_1_620b_accounting:
        rule: 1.620(b)
        deadline: Every 3 months
        description: Every receiver shall file in the same office an inventory and account under oath of any additional property or effects which the receiver has discovered or which shall have come to the receiver’s hands since appointment, and of th e amount remaining in the hands of or invested by the receiver
        trigger: Date of appointment, with filings every 3 months thereafter
        consequence: N/A
        extensions: Unless the court otherwise orders
        trigger_event: Ongoing filing
        trigger_parameters:
          original_text: Every 3 months unless the court o therwise orders, the receiver shall file in the same office an inventory and account under oath of any additional property or effects which the receiver has discovered or which shall have come to the receiver’s hands since appointment, and of th e amount remaining in the hands of or invested by the receiver
          jurisdiction: Florida
      rule_1_620b_order_compliance:
        rule: 1.620(b)
        deadline: not more than 20 days
        description: receiver to file inventory and account and to pay out of the receiver's own funds the expenses of the order and the proceedings thereon
        trigger: After being served with a copy of the court order
        consequence: N/A
        extensions: N/A
        trigger_event: Court Order
        trigger_parameters:
          original_text: the court shall enter an order requiring the receiver to file such inventory and account and to pay out of the receiver’s own funds the expenses of the order and the proceedings thereon w ithin not more than 20 days after being served with a copy of such order.
          jurisdiction: Florida
```
```yaml
jurisdiction: Florida
court_type: Civil
practice_area: General Civil
last_updated: '2025-06-05'
source: Florida Rules of Civil Procedure
extraction_method: Gemini 2.0 Flash Enhanced Analysis
extraction_date: '2025-06-13T12:00:00'
total_deadlines_extracted: 44
deadline_categories:
  arbitration:
    description: Deadlines related to voluntary binding arbitration.
    rules:
      arbitration_decision_filing:
        rule: "Rule 1.830(c)(1)"
        deadline: "within 10 days"
        description: "Serve the parties with notice of the decision and file the decision with the court."
        trigger: "final adjournment of the arbitration hearing"
        consequence: "Unspecified"
        extensions: "Unspecified"
        trigger_event: Adjournment
        trigger_parameters:
          original_text: "final adjournment of the arbitration hearing"
          jurisdiction: Florida
      arbitration_decision_appeal:
        rule: "Rule 1.830(c)(2)"
        deadline: "within 30 days"
        description: "Appeal a voluntary binding arbitration decision."
        trigger: "service of the decision on the parties"
        consequence: "The decision shall be referred to the presiding judge who shall enter such orders and judgments as required to carry out the terms of the decision."
        extensions: "Unspecified"
        trigger_event: Service
        trigger_parameters:
          original_text: "service of the decision on the parties"
          jurisdiction: Florida
  response_to_summons:
    description: Deadlines for responding to a summons.
    rules:
      general_summons_response:
        rule: "Form 1.902(a)"
        deadline: "within 20 days"
        description: "Serve written defenses to the complaint or petition."
        trigger: "after service of this summons on that defendant, exclusive of the day of service"
        consequence: "a default will be entered against that defendant for the relief demanded in the complaint or petition"
        extensions: "Unspecified"
        trigger_event: Service
        trigger_parameters:
          original_text: "after service of this summons on that defendant"
          jurisdiction: Florida
      state_agency_response:
        rule: "Form 1.902(a)"
        deadline: "40 days"
        description: "Serve written defenses to the complaint when the State of Florida, one of its agencies, or one of its officials or employees sued in his or her official capacity is a defendant."
        trigger: "after service of this summons on that defendant, exclusive of the day of service"
        consequence: "a default will be entered against that defendant for the relief demanded in the complaint or petition"
        extensions: "Unspecified"
        trigger_event: Service
        trigger_parameters:
          original_text: "after service of this summons on that defendant"
          jurisdiction: Florida
      section_768.28_response:
        rule: "Form 1.902(a)"
        deadline: "30 days"
        description: "Serve written defenses to the complaint when suit is brought pursuant to section 768.28, Florida Statutes."
        trigger: "after service of this summons on that defendant, exclusive of the day of service"
        consequence: "a default will be entered against that defendant for the relief demanded in the complaint or petition"
        extensions: "Unspecified"
        trigger_event: Service
        trigger_parameters:
          original_text: "after service of this summons on that defendant"
          jurisdiction: Florida
      crossclaim_response:
        rule: "Form 1.903"
        deadline: "within 20 days"
        description: "Serve written defenses to the crossclaim."
        trigger: "after service of this summons on that defendant, exclusive of the day of service"
        consequence: "a default will be entered against that defendant for the relief demanded in the crossclaim"
        extensions: "Unspecified"
        trigger_event: Service
        trigger_parameters:
          original_text: "after service of this summons on that defendant"
          jurisdiction: Florida
      third_party_response:
        rule: "Form 1.904"
        deadline: "within 20 days"
        description: "Serve written defenses to the third-party complaint or petition."
        trigger: "after service of this summons on that defendant, exclusive of the day of service"
        consequence: "a default will be entered against that defendant for the relief demanded in the third-party complaint or petition"
        extensions: "Unspecified"
        trigger_event: Service
        trigger_parameters:
          original_text: "after service of this summons on that defendant"
          jurisdiction: Florida
      garnishment_answer:
        rule: "Form 1.907(a)"
        deadline: "within 20 days"
        description: "Serve an answer to the writ of garnishment."
        trigger: "after service on the garnishee, exclusive of the day of service"
        consequence: "FAILURE TO FILE AN ANSWER WITHIN THE TIME REQUIRED MAY RESULT IN THE ENTRY OF JUDGMENT AGAINST THE GARNISHEE"
        extensions: "Unspecified"
        trigger_event: Service
        trigger_parameters:
          original_text: "after service on the garnishee"
          jurisdiction: Florida
      continuing_garnishment_answer:
        rule: "Form 1.907(b)"
        deadline: "within 20 days"
        description: "Serve an answer to the continuing writ of garnishment against salary or wages."
        trigger: "after service of this writ, exclusive of the day of service"
        consequence: "FAILURE TO FILE AN ANSWER WITHIN THE TIME REQUIRED MAY RESULT IN THE ENTRY OF JUDGMENT AGAINST THE GARNISHEE"
        extensions: "Unspecified"
        trigger_event: Service
        trigger_parameters:
          original_text: "after service of this writ"
          jurisdiction: Florida
  service_waiver:
    description: Deadlines related to waiver of service of process.
    rules:
      waiver_return_us:
        rule: "Form 1.902(c)(1)"
        deadline: "within 20 days"
        description: "Return a signed copy of the waiver of service of process."
        trigger: "after the date you receive this notice and request for waiver"
        consequence: "formal service of process may be initiated"
        extensions: "Unspecified"
        trigger_event: Receipt
        trigger_parameters:
          original_text: "after the date you receive this notice and request for waiver"
          jurisdiction: Florida
      waiver_return_outside_us:
        rule: "Form 1.902(c)(1)"
        deadline: "30 days"
        description: "Return a signed copy of the waiver of service of process if you do not reside in the United States."
        trigger: "after the date you receive this notice and request for waiver"
        consequence: "formal service of process may be initiated"
        extensions: "Unspecified"
        trigger_event: Receipt
        trigger_parameters:
          original_text: "after the date you receive this notice and request for waiver"
          jurisdiction: Florida
      response_after_waiver:
        rule: "Form 1.902(c)(1)"
        deadline: "60 days"
        description: "Respond to the complaint after returning the waiver of service of process."
        trigger: "after the date on which you received the notice and request for waiver"
        consequence: "a judgment may be entered against you"
        extensions: "Unspecified"
        trigger_event: Receipt
        trigger_parameters:
          original_text: "after the date on which you received the notice and request for waiver"
          jurisdiction: Florida
  distress_writ:
    description: Deadlines related to distress writs.
    rules:
      distress_writ_dissolution:
        rule: "Form 1.909"
        deadline: "20 days"
        description: "Move for dissolution of the distress writ."
        trigger: "from the time the complaint in this action is served"
        consequence: "The court may order the sheriff to levy on the property liable to distress forthwith."
        extensions: "Unspecified"
        trigger_event: Service
        trigger_parameters:
          original_text: "from the time the complaint in this action is served"
          jurisdiction: Florida
  eviction:
    description: Deadlines related to eviction summons.
    rules:
      eviction_response:
        rule: "Form 1.923(a)"
        deadline: "5 days"
        description: "Write down the reason(s) why you think you should not be forced to move, mail or take a copy of your written reason(s) to the landlord, and pay the clerk of court the rent that is due."
        trigger: "after the date these papers were given to you"
        consequence: "YOU MAY BE EVICTED WITHOUT A HEARING OR FURTHER NOTICE."
        extensions: "Not including Saturdays, Sundays, or legal holidays"
        trigger_event: Service
        trigger_parameters:
          original_text: "after the date these papers were given to you"
          jurisdiction: Florida
  back_rent_damages:
    description: Deadlines related to action for back rent or other damages.
    rules:
      back_rent_damages_response:
        rule: "Form 1.923(b)"
        deadline: "20 days"
        description: "Serve written defenses to the demand for back rent or any other damages alleged in the complaint."
        trigger: "after service of this summons on the defendant, exclusive of the day of service"
        consequence: "a default may be entered against the defendant for the relief demanded in that portion of the complaint"
        extensions: "Unspecified"
        trigger_event: Service
        trigger_parameters:
          original_text: "after service of this summons on the defendant"
          jurisdiction: Florida
  nonparty_production:
    description: Deadlines related to Notice of Production from Nonparty
    rules:
      notice_production_delivery:
        rule: "Form 1.921"
        deadline: "after 10 days"
        description: "The undersigned will issue or apply to the clerk of this court for issuance of the attached subpoena directed to nonparty."
        trigger: "from the date of service of this notice, if service is by delivery"
        consequence: "Unspecified"
        extensions: "Unspecified"
        trigger_event: Service
        trigger_parameters:
          original_text: "from the date of service of this notice, if service is by delivery"
          jurisdiction: Florida
      notice_production_mail:
        rule: "Form 1.921"
        deadline: "15 days"
        description: "The undersigned will issue or apply to the clerk of this court for issuance of the attached subpoena directed to nonparty."
        trigger: "from the date of service, if service is by mail"
        consequence: "Unspecified"
        extensions: "Unspecified"
        trigger_event: Service
        trigger_parameters:
          original_text: "from the date of service, if service is by mail"
          jurisdiction: Florida
  subpoena:
    description: Deadlines related to the procedure for obtaining accommodation to respond to a subpoena
    rules:
      subpoena_accommodation_trial:
        rule: "Form 1.910(a)"
        deadline: "at least 7 days"
        description: "Contact court personnel to request accommodation if you are a person with a disability"
        trigger: "before your scheduled court appearance"
        consequence: "Unspecified"
        extensions: "Unspecified"
        trigger_event: Scheduled Appearance
        trigger_parameters:
          original_text: "before your scheduled court appearance"
          jurisdiction: Florida
      subpoena_accommodation_trial_short_notice:
        rule: "Form 1.910(a)"
        deadline: "immediately upon receiving this notification"
        description: "Contact court personnel to request accommodation if you are a person with a disability"
        trigger: "if the time before the scheduled appearance is less than 7 days"
        consequence: "Unspecified"
        extensions: "Unspecified"
        trigger_event: Notification
        trigger_parameters:
          original_text: "if the time before the scheduled appearance is less than 7 days"
          jurisdiction: Florida
      subpoena_accommodation_trial_record:
        rule: "Form 1.910(b)"
        deadline: "at least 7 days"
        description: "Contact court personnel to request accommodation if you are a person with a disability"
        trigger: "before your scheduled court appearance"
        consequence: "Unspecified"
        extensions: "Unspecified"
        trigger_event: Scheduled Appearance
        trigger_parameters:
          original_text: "before your scheduled court appearance"
          jurisdiction: Florida
      subpoena_accommodation_trial_record_short_notice:
        rule: "Form 1.910(b)"
        deadline: "immediately upon receiving this notification"
        description: "Contact court personnel to request accommodation if you are a person with a disability"
        trigger: "if the time before the scheduled appearance is less than 7 days"
        consequence: "Unspecified"
        extensions: "Unspecified"
        trigger_event: Notification
        trigger_parameters:
          original_text: "if the time before the scheduled appearance is less than 7 days"
          jurisdiction: Florida
      subpoena_duces_tecum_accommodation_trial:
        rule: "Form 1.911(a)"
        deadline: "at least 7 days"
        description: "Contact court personnel to request accommodation if you are a person with a disability"
        trigger: "before your scheduled court appearance"
        consequence: "Unspecified"
        extensions: "Unspecified"
        trigger_event: Scheduled Appearance
        trigger_parameters:
          original_text: "before your scheduled court appearance"
          jurisdiction: Florida
      subpoena_duces_tecum_accommodation_trial_short_notice:
        rule: "Form 1.911(a)"
        deadline: "immediately upon receiving this notification"
        description: "Contact court personnel to request accommodation if you are a person with a disability"
        trigger: "if the time before the scheduled appearance is less than 7 days"
        consequence: "Unspecified"
        extensions: "Unspecified"
        trigger_event: Notification
        trigger_parameters:
          original_text: "if the time before the scheduled appearance is less than 7 days"
          jurisdiction: Florida
      subpoena_duces_tecum_accommodation_trial_record:
        rule: "Form 1.911(b)"
        deadline: "at least 7 days"
        description: "Contact court personnel to request accommodation if you are a person with a disability"
        trigger: "before your scheduled court appearance"
        consequence: "Unspecified"
        extensions: "Unspecified"
        trigger_event: Scheduled Appearance
        trigger_parameters:
          original_text: "before your scheduled court appearance"
          jurisdiction: Florida
      subpoena_duces_tecum_accommodation_trial_record_short_notice:
        rule: "Form 1.911(b)"
        deadline: "immediately upon receiving this notification"
        description: "Contact court personnel to request accommodation if you are a person with a disability"
        trigger: "if the time before the scheduled appearance is less than 7 days"
        consequence: "Unspecified"
        extensions: "Unspecified"
        trigger_event: Notification
        trigger_parameters:
          original_text: "if the time before the scheduled appearance is less than 7 days"
          jurisdiction: Florida
      subpoena_deposition_accommodation:
        rule: "Form 1.912(a)"
        deadline: "at least 7 days"
        description: "Contact attorney or party taking the deposition to request accommodation if you are a person with a disability"
        trigger: "before your scheduled deposition"
        consequence: "Unspecified"
        extensions: "Unspecified"
        trigger_event: Scheduled Deposition
        trigger_parameters:
          original_text: "before your scheduled deposition"
          jurisdiction: Florida
      subpoena_deposition_accommodation_short_notice:
        rule: "Form 1.912(a)"
        deadline: "immediately upon receiving this notification"
        description: "Contact attorney or party taking the deposition to request accommodation if you are a person with a disability"
        trigger: "if the time before the scheduled appearance is less than 7 days"
        consequence: "Unspecified"
        extensions: "Unspecified"
        trigger_event: Notification
        trigger_parameters:
          original_text: "if the time before the scheduled appearance is less than 7 days"
          jurisdiction: Florida
      subpoena_deposition_accommodation_record:
        rule: "Form 1.912(b)"
        deadline: "at least 7 days"
        description: "Contact attorney or party taking the deposition to request accommodation if you are a person with a disability"
        trigger: "before your scheduled deposition"
        consequence: "Unspecified"
        extensions: "Unspecified"
        trigger_event: Scheduled Deposition
        trigger_parameters:
          original_text: "before your scheduled deposition"
          jurisdiction: Florida
      subpoena_deposition_accommodation_record_short_notice:
        rule: "Form 1.912(b)"
        deadline: "immediately upon receiving this notification"
        description: "Contact attorney or party taking the deposition to request accommodation if you are a person with a disability"
        trigger: "if the time before the scheduled appearance is less than 7 days"
        consequence: "Unspecified"
        extensions: "Unspecified"
        trigger_event: Notification
        trigger_parameters:
          original_text: "if the time before the scheduled appearance is less than 7 days"
          jurisdiction: Florida
      subpoena_duces_tecum_deposition_accommodation:
        rule: "Form 1.913(a)"
        deadline: "at least 7 days"
        description: "Contact attorney or party taking the deposition to request accommodation if you are a person with a disability"
        trigger: "before your scheduled deposition"
        consequence: "Unspecified"
        extensions: "Unspecified"
        trigger_event: Scheduled Deposition
        trigger_parameters:
          original_text: "before your scheduled deposition"
          jurisdiction: Florida
      subpoena_duces_tecum_deposition_accommodation_short_notice:
        rule: "Form 1.913(a)"
        deadline: "immediately upon receiving this notification"
        description: "Contact attorney or party taking the deposition to request accommodation if you are a person with a disability"
        trigger: "if the time before the scheduled appearance is less than 7 days"
        consequence: "Unspecified"
        extensions: "Unspecified"
        trigger_event: Notification
        trigger_parameters:
          original_text: "if the time before the scheduled appearance is less than 7 days"
          jurisdiction: Florida
      subpoena_duces_tecum_deposition_accommodation_record:
        rule: "Form 1.913(b)"
        deadline: "at least 7 days"
        description: "Contact attorney or party taking the deposition to request accommodation if you are a person with a disability"
        trigger: "before your scheduled deposition"
        consequence: "Unspecified"
        extensions: "Unspecified"
        trigger_event: Scheduled Deposition
        trigger_parameters:
          original_text: "before your scheduled deposition"
          jurisdiction: Florida
      subpoena_duces_tecum_deposition_accommodation_record_short_notice:
        rule: "Form 1.913(b)"
        deadline: "immediately upon receiving this notification"
        description: "Contact attorney or party taking the deposition to request accommodation if you are a person with a disability"
        trigger: "if the time before the scheduled appearance is less than 7 days"
        consequence: "Unspecified"
        extensions: "Unspecified"
        trigger_event: Notification
        trigger_parameters:
          original_text: "if the time before the scheduled appearance is less than 7 days"
          jurisdiction: Florida
      subpoena_duces_tecum_no_deposition_accommodation_a:
        rule: "Form 1.922(a)"
        deadline: "at least 7 days"
        description: "Contact attorney or party taking the deposition to request accommodation if you are a person with a disability"
        trigger: "before your scheduled court appearance"
        consequence: "Unspecified"
        extensions: "Unspecified"
        trigger_event: Scheduled Appearance
        trigger_parameters:
          original_text: "before your scheduled court appearance"
          jurisdiction: Florida
      subpoena_duces_tecum_no_deposition_accommodation_short_notice_a:
        rule: "Form 1.922(a)"
        deadline: "immediately upon receiving this notification"
        description: "Contact attorney or party taking the deposition to request accommodation if you are a person with a disability"
        trigger: "if the time before the scheduled appearance is less than 7 days"
        consequence: "Unspecified"
        extensions: "Unspecified"
        trigger_event: Notification
        trigger_parameters:
          original_text: "if the time before the scheduled appearance is less than 7 days"
          jurisdiction: Florida
      subpoena_duces_tecum_no_deposition_accommodation_b:
        rule: "Form 1.922(b)"
        deadline: "at least 7 days"
        description: "Contact attorney or party taking the deposition to request accommodation if you are a person with a disability"
        trigger: "before your scheduled court appearance"
        consequence: "Unspecified"
        extensions: "Unspecified"
        trigger_event: Scheduled Appearance
        trigger_parameters:
          original_text: "before your scheduled court appearance"
          jurisdiction: Florida
      subpoena_duces_tecum_no_deposition_accommodation_short_notice_b:
        rule: "Form 1.922(b)"
        deadline: "immediately upon receiving this notification"
        description: "Contact attorney or party taking the deposition to request accommodation if you are a person with a disability"
        trigger: "if the time before the scheduled appearance is less than 7 days"
        consequence: "Unspecified"
        extensions: "Unspecified"
        trigger_event: Notification
        trigger_parameters:
          original_text: "if the time before the scheduled appearance is less than 7 days"
          jurisdiction: Florida
      subpoena_duces_tecum_no_deposition_accommodation_c:
        rule: "Form 1.922(c)"
        deadline: "at least 7 days"
        description: "Contact attorney or party taking the deposition to request accommodation if you are a person with a disability"
        trigger: "before your scheduled court appearance"
        consequence: "Unspecified"
        extensions: "Unspecified"
        trigger_event: Scheduled Appearance
        trigger_parameters:
          original_text: "before your scheduled court appearance"
          jurisdiction: Florida
      subpoena_duces_tecum_no_deposition_accommodation_short_notice_c:
        rule: "Form 1.922(c)"
        deadline: "immediately upon receiving this notification"
        description: "Contact attorney or party taking the deposition to request accommodation if you are a person with a disability"
        trigger: "if the time before the scheduled appearance is less than 7 days"
        consequence: "Unspecified"
        extensions: "Unspecified"
        trigger_event: Notification
        trigger_parameters:
          original_text: "if the time before the scheduled appearance is less than 7 days"
          jurisdiction: Florida
      subpoena_duces_tecum_no_deposition_accommodation_d:
        rule: "Form 1.922(d)"
        deadline: "at least 7 days"
        description: "Contact attorney or party taking the deposition to request accommodation if you are a person with a disability"
        trigger: "before your scheduled court appearance"
        consequence: "Unspecified"
        extensions: "Unspecified"
        trigger_event: Scheduled Appearance
        trigger_parameters:
          original_text: "before your scheduled court appearance"
          jurisdiction: Florida
      subpoena_duces_tecum_no_deposition_accommodation_short_notice_d:
        rule: "Form 1.922(d)"
        deadline: "immediately upon receiving this notification"
        description: "Contact attorney or party taking the deposition to request accommodation if you are a person with a disability"
        trigger: "if the time before the scheduled appearance is less than 7 days"
        consequence: "Unspecified"
        extensions: "Unspecified"
        trigger_event: Notification
        trigger_parameters:
          original_text: "if the time before the scheduled appearance is less than 7 days"
          jurisdiction: Florida
      ne_exeat_confinement:
        rule: "Form 1.917"
        deadline: "within 24 hours"
        description: "If the defendant does not give the bond, the defendant shall be brought before a judge of this court"
        trigger: "of confinement"
        consequence: "unspecified"
        extensions: "unspecified"
        trigger_event: Confinement
        trigger_parameters:
          original_text: "of confinement"
          jurisdiction: Florida
```
```yaml
jurisdiction: Florida
court_type: Civil
practice_area: General Civil Procedure
last_updated: '2025-06-05'
source: Florida Rules of Civil Procedure
extraction_method: Gemini 2.0 Flash Enhanced Analysis
extraction_date: '2025-06-05T12:00:00'
total_deadlines_extracted: 25 #Placeholder - replace after completing all extractions
deadline_categories:
  Pleading and Response Deadlines:
    description: Deadlines related to filing initial pleadings, answers, and other responsive documents.
    rules:
      rule_1:
        rule: "Form 1.944(c)"
        deadline: "20 days"
        description: "Defendant(s) shall appear at a hearing on foreclosure to show cause why the attached final judgment of foreclosure should not be entered against the defendant(s)."
        trigger: "Service of the order to show cause"
        consequence: "Failure to appear either in person or by an attorney at the show cause hearing or to file defenses by motion or by a verified or sworn answer, affidavits, or other papers which raise a genuine issue of material fact which would preclude entry of summary judgment or which would otherwise constitute a legal defense to foreclosure, after being served as provided by law with the order to show cause, will be deemed presumptively a waiver of the right to a hearing."
        extensions: "Not specified"
        trigger_event: "Service of order"
        trigger_parameters:
          original_text: "The date of the hearing may not occur sooner than the later of 20 days after service of the order to show cause"
          jurisdiction: Florida
      rule_2:
        rule: "Form 1.944(c)"
        deadline: "45 days"
        description: "Defendant(s) shall appear at a hearing on foreclosure to show cause why the attached final judgment of foreclosure should not be entered against the defendant(s)."
        trigger: "Service of the initial complaint"
        consequence: "Failure to appear either in person or by an attorney at the show cause hearing or to file defenses by motion or by a verified or sworn answer, affidavits, or other papers which raise a genuine issue of material fact which would preclude entry of summary judgment or which would otherwise constitute a legal defense to foreclosure, after being served as provided by law with the order to show cause, will be deemed presumptively a waiver of the right to a hearing."
        extensions: "Not specified"
        trigger_event: "Service of initial complaint"
        trigger_parameters:
          original_text: "The date of the hearing may not occur sooner than the later of 20 days after service of the order to show cause or 45 days after service of the initial complaint."
          jurisdiction: Florida
      rule_3:
        rule: "Form 1.944(c)"
        deadline: "30 days"
        description: "Defendant(s) shall appear at a hearing on foreclosure to show cause why the attached final judgment of foreclosure should not be entered against the defendant(s)."
        trigger: "First publication when service is obtained by publication"
        consequence: "Failure to appear either in person or by an attorney at the show cause hearing or to file defenses by motion or by a verified or sworn answer, affidavits, or other papers which raise a genuine issue of material fact which would preclude entry of summary judgment or which would otherwise constitute a legal defense to foreclosure, after being served as provided by law with the order to show cause, will be deemed presumptively a waiver of the right to a hearing."
        extensions: "Not specified"
        trigger_event: "First publication"
        trigger_parameters:
          original_text: "When service is obtained by publication, the date for the hearing may not be set sooner than 30 days after the first publication."
          jurisdiction: Florida
      rule_4:
        rule: "Form 1.996(a)"
        deadline: "10 days"
        description: "Property owner contact clerk of court to see if there is additional money from the foreclosure sale that the clerk has in the registry of the court."
        trigger: "After the Sale"
        consequence: "If the property is sold at public auction, there may be additional money from the sale after payment of persons who are entitled to be paid from the sale proceeds pursuant to the final judgment."
        extensions: "Not specified"
        trigger_event: "Foreclosure sale"
        trigger_parameters:
          original_text: "PLEASE CHECK WITH THE CLERK OF THE COURT, (INSERT INFORMATION FOR APPLICABLE COURT) WITHIN 10 DAYS AFTER THE SALE TO SEE IF THERE IS ADDITIONAL MONEY FROM THE FORECLOSURE SALE THAT THE CLERK HAS IN THE REGISTRY OF THE COURT."
          jurisdiction: Florida
      rule_5:
        rule: "Form 1.989(a)"
        deadline: "10 months"
        description: "Period of inactivity before Notice of Lack of Prosecution can be served"
        trigger: "Lack of Record Activity"
        consequence: "Case may be dismissed for lack of prosecution"
        extensions: "Stay may be issued or approved by the court"
        trigger_event: "Lack of record activity"
        trigger_parameters:
          original_text: "PLEASE TAKE NOTICE that it appears on the face of the record that no activity by filing of pleadings, order of court, or otherwise has occurred for a period of 10 months immediately preceding service of this notice, and no stay has been issued or approved by the court."
          jurisdiction: Florida
      rule_6:
        rule: "Form 1.989(a)"
        deadline: "60 days"
        description: "Period after service of Notice of Lack of Prosecution for party to show good cause in writing why the action should remain pending."
        trigger: "Service of the Notice of Lack of Prosecution"
        consequence: "Action may be dismissed by the court on its own motion or on the motion of any interested person."
        extensions: "Stay may be issued or approved during this 60-day period"
        trigger_event: "Service of Notice"
        trigger_parameters:
          original_text: "Pursuant to rule 1.420(e), if no such record activity occurs within 60 days following the service of this notice, and if no stay is issued or approved during such 60-day period, this action may be dismissed by the court on its own motion or on the motion of any interested person, whether a party to the action or not, after reasonable notice to the parties, unless a party shows good cause in writing at least 5 days before the hearing on the motion why the action should remain pending."
          jurisdiction: Florida
      rule_7:
        rule: "Form 1.989(a)"
        deadline: "5 days"
        description: "Deadline to show good cause in writing before the hearing on the motion for lack of prosecution."
        trigger: "Before hearing on motion"
        consequence: "Case may be dismissed for lack of prosecution."
        extensions: "Not specified"
        trigger_event: "Hearing"
        trigger_parameters:
          original_text: "unless a party shows good cause in writing at least 5 days before the hearing on the motion why the action should remain pending."
          jurisdiction: Florida
  Discovery Deadlines:
    description: Deadlines related to discovery requests, responses, and production.
    rules:
      rule_8:
        rule: "Form 1.977"
        deadline: "Not specified, but implied to be promptly"
        description: "Judgment debtor to mail or deliver completed Fact Information Sheet with attachments to the judgment creditor or the judgment creditor’s attorney."
        trigger: "Service of Fact Information Sheet"
        consequence: "Failure to comply may result in contempt of court."
        extensions: "Not specified"
        trigger_event: "Service"
        trigger_parameters:
          original_text: "YOU MUST MAIL OR DELIVER THIS COMPLETED FORM, WITH ALL ATTACHMENTS, TO THE JUDGMENT CREDITOR OR THE JUDGMENT CREDITOR’S ATTORNEY, BUT DO NOT FILE THIS FORM WITH THE CLERK OF COURT."
          jurisdiction: Florida
  Motion Practice Deadlines:
    description: Deadlines related to filing and responding to motions.
    rules:
      rule_9:
        rule: "Various"
        deadline: "7 days"
        description: "Contact Court to request accommodations for disability"
        trigger: "Scheduled court appearance"
        consequence: "May not be able to participate effectively in the proceeding."
        extensions: "Immediately upon receiving notice if time is less than 7 days"
        trigger_event: "Court Appearance"
        trigger_parameters:
          original_text: "Please contact [identify applicable court personnel by name, address, and telephone number] at least 7 days before your scheduled court appearance, or immediately upon receiving this notification if the time before your scheduled appearance is less than 7 days; if you are hearing or voice impaired, call 711."
          jurisdiction: Florida
  Trial and Hearing Deadlines:
    description: Deadlines related to preparing for trial, including witness lists, exhibit lists, and motions in limine.
    rules:
      rule_10:
        rule: "Form 1.982"
        deadline: "7 days"
        description: "Contact court personnel for accommodations related to disabilities."
        trigger: "Scheduled court appearance"
        consequence: "If accommodations are not arranged prior to hearing, party's ability to participate effectively may be impacted."
        extensions: "Immediately if notification is received less than 7 days prior to appearance"
        trigger_event: "Court hearing"
        trigger_parameters:
          original_text: "Please contact [identify applicable court personnel by name, address, and telephone number] at least 7 days before your scheduled court appearance, or immediately upon receiving this notification if the time before your scheduled appearance is less than 7 days; if you are hearing or voice impaired, call 711."
          jurisdiction: Florida
  Case Management Deadlines:
    description: Deadlines set by the court for various case management tasks.
    rules:
      rule_11:
        rule: "Form 1.996(a)"
        deadline: "10 days"
        description: "Deadline for a subordinate lienholder to file a claim with the clerk for funds remaining after a foreclosure sale."
        trigger: "Date the clerk reports the funds as unclaimed."
        consequence: "Failure to file a timely claim will result in not being entitled to any remaining funds."
        extensions: "None specified."
        trigger_event: "Unclaimed funds"
        trigger_parameters:
          original_text: "IF YOU ARE A SUBORDINATE LIENHOLDER CLAIMING A RIGHT TO FUNDS REMAINING AFTER THE SALE, IF ANY, YOU MUST FILE A CLAIM WITH THE CLERK NO LATER THAN THE DATE THAT THE CLERK REPORTS THE FUNDS AS UNCLAIMED. IF YOU FAIL TO FILE A TIMELY CLAIM, YOU WILL NOT BE ENTITLED TO ANY REMAINING FUNDS."
          jurisdiction: Florida
  Appeal and Post-Trial Deadlines:
    description: Deadlines related to filing appeals and post-trial motions.
    rules:
      rule_12:
          rule: "Form 1.996(b)"
          deadline: "10 days"
          description: "Property owner to check with the clerk of court after the sale to see if there is additional money from the foreclosure sale"
          trigger: "Foreclosure Sale"
          consequence: "Failure to contact court may result in not being able to access remaining funds"
          extensions: "None specified."
          trigger_event: "Foreclosure Sale"
          trigger_parameters:
            original_text: "PLEASE CHECK WITH THE CLERK OF THE COURT, (INSERT INFORMATION FOR APPLICABLE COURT) WITHIN 10 DAYS AFTER THE SALE TO SEE IF THERE IS ADDITIONAL MONEY FROM THE FORECLOSURE SALE THAT THE CLERK HAS IN THE REGISTRY OF THE COURT."
            jurisdiction: Florida

  Service and Notice Deadlines:
    description: Deadlines for serving legal documents and providing notices to parties.
    rules: {}

  Emergency and Expedited Deadlines:
    description: Deadlines in emergency or expedited proceedings.
    rules: {}
```
```yaml
jurisdiction: Florida
court_type: Civil
practice_area: General Civil
last_updated: '2025-06-05'
source: Florida Rules of Civil Procedure
extraction_method: Gemini 2.0 Flash Enhanced Analysis
extraction_date: '2025-06-13T12:00:00'
total_deadlines_extracted: 0 #Placeholder - Actual count requires a full rule set
deadline_categories:
  pleading_and_response_deadlines:
    description: Deadlines related to filing pleadings and responding to them.
    rules: {} # No deadlines extracted

  discovery_deadlines:
    description: Deadlines for various discovery procedures like interrogatories, requests for production, and depositions.
    rules: {} # No deadlines extracted

  motion_practice_deadlines:
    description: Deadlines related to filing and responding to motions.
    rules: {} # No deadlines extracted

  trial_and_hearing_deadlines:
    description: Deadlines related to trial preparation, scheduling, and conduct.
    rules: {} # No deadlines extracted

  case_management_deadlines:
    description: Deadlines set by the court for case management and pretrial conferences.
    rules: {} # No deadlines extracted

  appeal_and_post_trial_deadlines:
    description: Deadlines for filing appeals and post-trial motions.
    rules: {} # No deadlines extracted

  service_and_notice_deadlines:
    description: Deadlines for serving legal documents and providing notice to parties.
    rules: {} # No deadlines extracted

  emergency_and_expedited_deadlines:
    description: Expedited deadlines in emergency situations.
    rules: {} # No deadlines extracted
```