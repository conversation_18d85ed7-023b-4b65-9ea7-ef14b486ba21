# MCP Rules Engine Integration Guide for Core Ailex

## 🔐 Authentication Setup

The MCP Rules Engine uses **dual authentication** for maximum security:
1. **Google Cloud Run Authentication** (OAuth2/Service Account)
2. **API Key Authentication** (Custom header)

## 📋 Service Information

### Staging Environment
- **URL**: `https://mcp-staging-************.us-central1.run.app`
- **API Key**: `mcp-rules-2025-staging`
- **Service Account**: `<EMAIL>`

### Production Environment (To be deployed)
- **URL**: `https://mcp-prod-************.us-central1.run.app`
- **API Key**: `mcp-rules-2025-production`
- **Service Account**: `<EMAIL>`

## 🚀 Integration Steps for Core Ailex

### Step 1: Add Service Account Key to Core Ailex

1. Copy the `mcp-client-key.json` file to your Core Ailex repository
2. Add it to your `.gitignore` file (NEVER commit service account keys)
3. Set up environment variables:

```bash
# In Core Ailex .env file
MCP_RULES_ENGINE_URL=https://mcp-staging-************.us-central1.run.app
MCP_API_KEY=mcp-rules-2025-staging
GOOGLE_APPLICATION_CREDENTIALS=./mcp-client-key.json
```

### Step 2: Install Google Auth Library

```bash
# For Node.js/TypeScript
npm install google-auth-library

# For Python
pip install google-auth google-auth-oauthlib google-auth-httplib2
```

### Step 3: Implementation Examples

#### Node.js/TypeScript Implementation

```typescript
import { GoogleAuth } from 'google-auth-library';
import fetch from 'node-fetch';

class MCPRulesEngineClient {
  private auth: GoogleAuth;
  private serviceUrl: string;
  private apiKey: string;

  constructor() {
    this.auth = new GoogleAuth({
      scopes: ['https://www.googleapis.com/auth/cloud-platform'],
      keyFilename: process.env.GOOGLE_APPLICATION_CREDENTIALS
    });
    this.serviceUrl = process.env.MCP_RULES_ENGINE_URL!;
    this.apiKey = process.env.MCP_API_KEY!;
  }

  async calculateDeadlines(jurisdiction: string, triggerCode: string, startDate: string) {
    try {
      // Get access token
      const client = await this.auth.getClient();
      const accessToken = await client.getAccessToken();

      // Make authenticated request
      const response = await fetch(`${this.serviceUrl}/mcp/run`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken.token}`,
          'x-api-key': this.apiKey,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          toolName: 'calculate_deadlines',
          params: {
            jurisdiction,
            triggerCode,
            startDate
          }
        })
      });

      if (!response.ok) {
        throw new Error(`MCP API error: ${response.status} ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error calling MCP Rules Engine:', error);
      throw error;
    }
  }

  async healthCheck() {
    try {
      const client = await this.auth.getClient();
      const accessToken = await client.getAccessToken();

      const response = await fetch(`${this.serviceUrl}/health/detailed`, {
        headers: {
          'Authorization': `Bearer ${accessToken.token}`,
          'x-api-key': this.apiKey
        }
      });

      return await response.json();
    } catch (error) {
      console.error('Health check failed:', error);
      throw error;
    }
  }
}

// Usage example
const mcpClient = new MCPRulesEngineClient();

// Calculate deadlines for a Texas arrest
const deadlines = await mcpClient.calculateDeadlines(
  'TX_STATE', 
  'ARREST_BOOKING', 
  '2025-01-15'
);
```

#### Python Implementation

```python
import os
import json
import requests
from google.auth.transport.requests import Request
from google.oauth2 import service_account

class MCPRulesEngineClient:
    def __init__(self):
        self.service_url = os.getenv('MCP_RULES_ENGINE_URL')
        self.api_key = os.getenv('MCP_API_KEY')
        
        # Load service account credentials
        credentials_path = os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
        self.credentials = service_account.Credentials.from_service_account_file(
            credentials_path,
            scopes=['https://www.googleapis.com/auth/cloud-platform']
        )
    
    def _get_access_token(self):
        """Get a fresh access token"""
        self.credentials.refresh(Request())
        return self.credentials.token
    
    def calculate_deadlines(self, jurisdiction, trigger_code, start_date):
        """Calculate legal deadlines"""
        access_token = self._get_access_token()
        
        headers = {
            'Authorization': f'Bearer {access_token}',
            'x-api-key': self.api_key,
            'Content-Type': 'application/json'
        }
        
        payload = {
            'toolName': 'calculate_deadlines',
            'params': {
                'jurisdiction': jurisdiction,
                'triggerCode': trigger_code,
                'startDate': start_date
            }
        }
        
        response = requests.post(
            f'{self.service_url}/mcp/run',
            headers=headers,
            json=payload
        )
        
        response.raise_for_status()
        return response.json()

# Usage example
mcp_client = MCPRulesEngineClient()
deadlines = mcp_client.calculate_deadlines('TX_STATE', 'ARREST_BOOKING', '2025-01-15')
```

## 🔒 Security Best Practices

1. **Never commit service account keys** to version control
2. **Use environment variables** for all sensitive configuration
3. **Rotate service account keys** regularly (every 90 days)
4. **Monitor API usage** through Cloud Run logs
5. **Use different API keys** for staging vs production

## 🧪 Testing the Integration

Use the provided test script to verify the connection:

```bash
# Test staging environment
./test-core-ailex-integration.sh staging

# Test production environment (when deployed)
./test-core-ailex-integration.sh production
```

## 📊 Available Endpoints

### POST /mcp/run
Calculate legal deadlines for a given jurisdiction and trigger.

**Request:**
```json
{
  "toolName": "calculate_deadlines",
  "params": {
    "jurisdiction": "TX_STATE",
    "triggerCode": "ARREST_BOOKING",
    "startDate": "2025-01-15"
  }
}
```

### GET /health
Basic health check (public endpoint)

### GET /health/detailed
Detailed health check with system information (requires API key)

## 🚨 Error Handling

- **401 Unauthorized**: Invalid or missing access token
- **403 Forbidden**: Invalid API key or insufficient permissions
- **400 Bad Request**: Invalid request format or parameters
- **404 Not Found**: Jurisdiction or trigger code not found
- **500 Internal Server Error**: Server-side processing error

## 📞 Support

For integration support, contact the MCP Rules Engine team or check the logs:

```bash
# View service logs
gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=mcp-staging" --limit=50
```
