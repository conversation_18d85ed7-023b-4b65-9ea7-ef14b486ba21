name: Deploy to Cloud Run

on:
  push:
    branches:
      - main
      - staging
  pull_request:
    branches:
      - main

env:
  PROJECT_ID: texas-laws-personalinjury
  REGION: us-central1

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run tests
      run: npm test

    - name: Build application
      run: npm run build

  deploy-staging:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/staging' || github.event_name == 'pull_request'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Google Cloud CLI
      uses: google-github-actions/setup-gcloud@v2
      with:
        project_id: ${{ env.PROJECT_ID }}
        service_account_key: ${{ secrets.GCP_SA_KEY }}
        export_default_credentials: true

    - name: Configure Docker for GCR
      run: gcloud auth configure-docker

    - name: Deploy to Cloud Run (Staging)
      run: |
        gcloud run deploy mcp-staging \
          --source=. \
          --region=${{ env.REGION }} \
          --platform=managed \
          --allow-unauthenticated \
          --port=4000 \
          --memory=1Gi \
          --cpu=1 \
          --min-instances=0 \
          --max-instances=10 \
          --timeout=300 \
          --concurrency=80 \
          --set-env-vars="NODE_ENV=staging,PORT=4000" \
          --tag=staging

    - name: Get staging URL
      id: staging-url
      run: |
        URL=$(gcloud run services describe mcp-staging --region=${{ env.REGION }} --format="value(status.url)")
        echo "url=$URL" >> $GITHUB_OUTPUT

    - name: Test staging deployment
      run: |
        chmod +x deployment/test-deployment.sh
        ./deployment/test-deployment.sh staging ${{ steps.staging-url.outputs.url }}

  deploy-production:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Google Cloud CLI
      uses: google-github-actions/setup-gcloud@v2
      with:
        project_id: ${{ env.PROJECT_ID }}
        service_account_key: ${{ secrets.GCP_SA_KEY }}
        export_default_credentials: true

    - name: Configure Docker for GCR
      run: gcloud auth configure-docker

    - name: Deploy to Cloud Run (Production)
      run: |
        gcloud run deploy mcp-prod \
          --source=. \
          --region=${{ env.REGION }} \
          --platform=managed \
          --allow-unauthenticated \
          --port=4000 \
          --memory=1Gi \
          --cpu=1 \
          --min-instances=1 \
          --max-instances=20 \
          --timeout=300 \
          --concurrency=80 \
          --set-env-vars="NODE_ENV=production,PORT=4000" \
          --tag=prod

    - name: Get production URL
      id: prod-url
      run: |
        URL=$(gcloud run services describe mcp-prod --region=${{ env.REGION }} --format="value(status.url)")
        echo "url=$URL" >> $GITHUB_OUTPUT

    - name: Test production deployment
      run: |
        chmod +x deployment/test-deployment.sh
        ./deployment/test-deployment.sh production ${{ steps.prod-url.outputs.url }}

    - name: Notify deployment success
      run: |
        echo "🚀 Production deployment successful!"
        echo "Service URL: ${{ steps.prod-url.outputs.url }}"
