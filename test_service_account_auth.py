#!/usr/bin/env python3

import json
import requests
from google.auth.transport.requests import Request
from google.oauth2 import service_account

def test_service_account_auth():
    """Test service account authentication with MCP Rules Engine"""
    
    # Load service account credentials
    try:
        with open('mcp-client-key.json', 'r') as f:
            service_account_info = json.load(f)
        
        credentials = service_account.Credentials.from_service_account_info(
            service_account_info,
            scopes=['https://www.googleapis.com/auth/cloud-platform']
        )
        
        # Refresh credentials to get access token
        credentials.refresh(Request())
        access_token = credentials.token
        
        print(f"✅ Successfully obtained access token: {access_token[:20]}...")
        
        # Test the health endpoint
        service_url = "https://mcp-staging-v2-************.us-central1.run.app"
        api_key = "mcp-rules-2025-staging"
        
        headers = {
            'Authorization': f'Bearer {access_token}',
            'x-api-key': api_key
        }
        
        print(f"🧪 Testing health endpoint...")
        response = requests.get(f'{service_url}/health', headers=headers)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            print("✅ Health check passed!")
        else:
            print(f"❌ Health check failed with status {response.status_code}")
        
        # Test the MCP endpoint
        print(f"\n🧪 Testing MCP endpoint...")
        mcp_payload = {
            'toolName': 'calculate_deadlines',
            'params': {
                'jurisdiction': 'TX_STATE',
                'triggerCode': 'ARREST_BOOKING',
                'startDate': '2025-01-15'
            }
        }
        
        headers['Content-Type'] = 'application/json'
        response = requests.post(f'{service_url}/mcp/run', headers=headers, json=mcp_payload)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            print("✅ MCP endpoint test passed!")
        else:
            print(f"❌ MCP endpoint test failed with status {response.status_code}")
            
    except FileNotFoundError:
        print("❌ Error: mcp-client-key.json not found")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_service_account_auth()
