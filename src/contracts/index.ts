import { z } from "zod";

// Practice area enum for validation
export const PracticeAreaEnum = z.enum(['personal_injury', 'criminal_defense', 'family_law']);

export const ComputeRequest = z.object({
  jurisdiction: z.string(),           // "TX_STATE"
  triggerCode: z.string(),            // "SERVICE_OF_PROCESS"
  startDate: z.string().date(),       // ISO 8601
  practiceArea: PracticeAreaEnum.optional()  // Optional practice area
});
export type ComputeRequest = z.infer<typeof ComputeRequest>;

export const Deadline = z.object({
  deadlineCode: z.string(),
  deadlineDate: z.string(),
  ruleCitation: z.string(),
  calcSteps: z.array(z.string())
});
export type Deadline = z.infer<typeof Deadline>;

export const ComputeResponse = z.object({
  deadlines: z.array(Deadline)
});
export type ComputeResponse = z.infer<typeof ComputeResponse>;
