import express, { Request, Response } from 'express';
import { z } from 'zod';
import { loadRules, getRules, areRulesLoaded } from './loaders/ruleLoader.js';
import { loadHolidays, areHolidaysLoaded } from './loaders/holidayLoader.js';
import { calculateDeadlines } from './engine/calculator.js';

// Define ComputeRequest interface locally
interface ComputeRequest {
    jurisdiction: string;
    triggerCode: string;
    startDate: string; // YYYY-MM-DD string format
    practiceArea?: string; // Optional practice area
}

const app = express();
app.use(express.json());

// Basic request logging
app.use((req: Request, res: Response, next: express.NextFunction) => {
    console.log(`${req.method} ${req.path} - Headers:`, JSON.stringify(req.headers, null, 2));
    next();
});

// Simple API key middleware for Cloud Run
const apiKeyAuth = (req: Request, res: Response, next: express.NextFunction) => {
  // Log all headers for debugging
  console.log('Incoming request headers:', JSON.stringify(req.headers, null, 2));

  // Skip authentication if behind API Gateway (detected by specific headers)
  const isFromApiGateway = req.headers['x-forwarded-for'] &&
                          (req.headers['user-agent']?.includes('GoogleHC') ||
                           req.headers['user-agent']?.includes('Google-Cloud-Functions') ||
                           req.headers['x-cloud-trace-context']);

  if (isFromApiGateway) {
    console.log('Request from API Gateway detected, skipping backend auth');
    next();
    return;
  }

  const apiKey = req.headers['x-api-key'] || req.query.apiKey;
  const validApiKey = process.env.API_KEY || 'mcp-rules-2025';

  if (apiKey === validApiKey) {
    next();
  } else {
    console.log('Authentication failed. API key:', apiKey, 'Expected:', validApiKey);
    res.status(401).json({ error: 'Invalid or missing API key. Use x-api-key header or apiKey query parameter.' });
  }
};

const PORT = parseInt(process.env.PORT || '3000', 10);

// Define expected input schema for the calculate_deadlines tool
const CalculateDeadlinesParamsSchema = z.object({
    jurisdiction: z.string().min(1),
    triggerCode: z.string().min(1),
    startDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD
    practiceArea: z.enum(['personal_injury', 'criminal_defense', 'family_law']).optional(), // Optional practice area
});

// Define the MCP request body schema
const McpRequestBodySchema = z.object({
    toolName: z.literal('calculate_deadlines'),
    params: CalculateDeadlinesParamsSchema,
});

// MCP Endpoint (protected with API key)
app.post('/mcp/run', apiKeyAuth, async (req: Request, res: Response): Promise<void> => {
    try {
        // Validate request body
        const validationResult = McpRequestBodySchema.safeParse(req.body);
        if (!validationResult.success) {
            res.status(400).json({ error: 'Invalid MCP request body', details: validationResult.error.errors });
            return;
        }

        const { toolName, params } = validationResult.data;
        const { jurisdiction, triggerCode, startDate, practiceArea } = params;

        // Parse start date and extract year
        const startDateObj = new Date(startDate);
        if (isNaN(startDateObj.getTime())) {
             res.status(400).json({ error: 'Invalid startDate format. Use YYYY-MM-DD.' });
             return;
        }
        const year = startDateObj.getFullYear();

        // Ensure rules and holidays are loaded for the required jurisdiction and year
        if (!areRulesLoaded(jurisdiction, practiceArea)) {
            const contextInfo = practiceArea ? `${jurisdiction} - ${practiceArea}` : jurisdiction;
            console.log(`Rules for ${contextInfo} not loaded. Loading...`);
            await loadRules(jurisdiction, practiceArea);
        }
        if (!areHolidaysLoaded(jurisdiction, year)) {
            console.log(`Holidays for ${jurisdiction} / ${year} not loaded. Loading...`);
            await loadHolidays(year);
        }

        // Now, explicitly check if rules were actually loaded before proceeding
        const rules = getRules(jurisdiction, practiceArea);
        if (!rules) {
             const contextInfo = practiceArea ? `${jurisdiction} - ${practiceArea}` : jurisdiction;
             res.status(404).json({ error: `Rules not found or failed to load for: ${contextInfo}` });
             return;
        }

        // Prepare the request object for the calculator function
        const computeRequest: ComputeRequest = {
            jurisdiction: jurisdiction,
            triggerCode: triggerCode,
            startDate: startDate,
            practiceArea: practiceArea
        };

        // Calculate deadlines using the single request object
        const deadlines = calculateDeadlines(computeRequest);

        // Return success response
        res.status(200).json({ result: deadlines });
        return;

    } catch (error: any) {
        console.error('Error processing MCP request:', error);
        res.status(500).json({ error: 'Internal server error', message: error.message });
        return;
    }
});

// Basic health check endpoint (public - no auth required)
app.get('/health', (req: Request, res: Response) => {
    res.status(200).json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Protected health check with system info
app.get('/health/detailed', apiKeyAuth, (req: Request, res: Response) => {
    res.status(200).json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV || 'development',
        port: PORT
    });
});

app.listen(PORT, '0.0.0.0', () => {
    console.log(`MCP Rules Engine server listening on http://0.0.0.0:${PORT}`);
    // Optional: Pre-load common rules/holidays on startup if desired
    // loadRules('TX_STATE').catch(console.error);
    // loadHolidays(new Date().getFullYear()).catch(console.error);
});
