/**
 * Trigger Code Mapper
 * 
 * Maps expected trigger codes from tests/API to actual trigger codes in rule files.
 * This allows us to maintain backward compatibility while standardizing trigger codes.
 */

export interface TriggerMapping {
  expectedCode: string;
  actualCode: string;
  description: string;
  practiceAreas: string[];
}

// Mapping from expected trigger codes to actual trigger codes in rule files
export const TRIGGER_CODE_MAPPINGS: Record<string, TriggerMapping> = {
  // Direct mappings (expected code matches actual code)
  'SERVICE_OF_PROCESS': {
    expectedCode: 'SERVICE_OF_PROCESS',
    actualCode: 'SERVICE_OF_PROCESS',
    description: 'Service of process on defendant',
    practiceAreas: ['personal_injury', 'criminal_defense', 'family_law']
  },
  
  'ARRAIGNMENT': {
    expectedCode: 'ARRAIGNMENT',
    actualCode: 'ARRAIGNMENT',
    description: 'Defendant arraignment',
    practiceAreas: ['criminal_defense']
  },
  
  'CHARGES_FILED': {
    expectedCode: 'CHARGES_FILED',
    actualCode: 'CHARGES_FILED',
    description: 'Criminal charges filed',
    practiceAreas: ['criminal_defense']
  },
  
  'PETITION_SERVICE': {
    expectedCode: 'PETITION_SERVICE',
    actualCode: 'PETITION_SERVICE',
    description: 'Service of petition',
    practiceAreas: ['family_law']
  },
  
  'LAST_PLEADING_SERVICE': {
    expectedCode: 'LAST_PLEADING_SERVICE',
    actualCode: 'LAST_PLEADING_SERVICE',
    description: 'Service of last pleading',
    practiceAreas: ['personal_injury', 'family_law']
  },
  
  'ARREST_BOOKING': {
    expectedCode: 'ARREST_BOOKING',
    actualCode: 'ARREST_BOOKING',
    description: 'Arrest and booking of defendant',
    practiceAreas: ['criminal_defense', 'family_law']
  },
  
  'DIVORCE_DECREED': {
    expectedCode: 'DIVORCE_DECREED',
    actualCode: 'DIVORCE_DECREED',
    description: 'Divorce decree entered',
    practiceAreas: ['family_law']
  },

  // Mapped codes (expected code maps to different actual code)
  'STATE_ENTITY_SERVICE': {
    expectedCode: 'STATE_ENTITY_SERVICE',
    actualCode: 'SERVICE_OF_PROCESS',
    description: 'Service of process on state entity (maps to SERVICE_OF_PROCESS)',
    practiceAreas: ['personal_injury']
  },
  
  'DIVORCE_FILED': {
    expectedCode: 'DIVORCE_FILED',
    actualCode: 'DIVORCE_DECREED',
    description: 'Divorce filed (maps to DIVORCE_DECREED for waiting period)',
    practiceAreas: ['family_law']
  }
};

// Additional common trigger codes found in rule files
export const ADDITIONAL_TRIGGER_CODES = [
  'EVENT_OCCURS',
  'MOTION_FILED', 
  'NOTICE_RECEIVED',
  'DOCUMENT_FILED',
  'JUDGMENT_ENTERED',
  'CHILD_BORN',
  'MARRIAGE_CELEBRATED'
];

/**
 * Maps an expected trigger code to the actual trigger code used in rule files
 */
export function mapTriggerCode(expectedCode: string): string {
  const mapping = TRIGGER_CODE_MAPPINGS[expectedCode];
  if (mapping) {
    return mapping.actualCode;
  }
  
  // If no mapping found, return the original code
  // This allows for direct use of actual trigger codes
  return expectedCode;
}

/**
 * Gets the reverse mapping (actual code to expected code)
 */
export function getExpectedTriggerCode(actualCode: string): string {
  for (const [expectedCode, mapping] of Object.entries(TRIGGER_CODE_MAPPINGS)) {
    if (mapping.actualCode === actualCode) {
      return expectedCode;
    }
  }
  
  // If no reverse mapping found, return the actual code
  return actualCode;
}

/**
 * Checks if a trigger code is valid for a given practice area
 */
export function isValidTriggerForPracticeArea(triggerCode: string, practiceArea: string): boolean {
  const mapping = TRIGGER_CODE_MAPPINGS[triggerCode];
  if (mapping) {
    return mapping.practiceAreas.includes(practiceArea);
  }
  
  // If not in mappings, check if it's in additional codes (assume valid for all)
  return ADDITIONAL_TRIGGER_CODES.includes(triggerCode);
}

/**
 * Gets all valid trigger codes for a practice area
 */
export function getValidTriggersForPracticeArea(practiceArea: string): string[] {
  const validTriggers: string[] = [];
  
  // Add mapped trigger codes
  for (const [expectedCode, mapping] of Object.entries(TRIGGER_CODE_MAPPINGS)) {
    if (mapping.practiceAreas.includes(practiceArea)) {
      validTriggers.push(expectedCode);
    }
  }
  
  // Add additional trigger codes (available for all practice areas)
  validTriggers.push(...ADDITIONAL_TRIGGER_CODES);
  
  return validTriggers;
}

/**
 * Gets mapping information for a trigger code
 */
export function getTriggerMapping(expectedCode: string): TriggerMapping | null {
  return TRIGGER_CODE_MAPPINGS[expectedCode] || null;
}
