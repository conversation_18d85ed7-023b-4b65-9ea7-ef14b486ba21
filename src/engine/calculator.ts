import { addDays, isSaturday, isSunday, addBusinessDays } from "date-fns";
import { getRule } from "../loaders/ruleLoader.js";
import { isHoliday } from "../loaders/holidayLoader.js";
import type { ComputeRequest, Deadline } from "../contracts/index.js";

export function calculateDeadlines(req: ComputeRequest): Deadline[] {
  const rule = getRule(req.jurisdiction, req.triggerCode, req.practiceArea);
  const start = new Date(req.startDate);

  return rule.deadlines.map((dl: any) => {
    let d: Date;
    let initialCalcDesc: string;

    // Calculate initial date based on offset unit
    if (dl.offsetUnit === 'business') {
      d = addBusinessDays(start, dl.offset);
      initialCalcDesc = `+${dl.offset} business days → ${d.toISOString().slice(0,10)}`;
    } else { // Default to calendar days
      d = addDays(start, dl.offset);
      initialCalcDesc = `+${dl.offset} calendar days → ${d.toISOString().slice(0,10)}`;
    }
    
    const steps = [`Start ${req.startDate}`, initialCalcDesc];

    // weekend/holiday roll (handles holidays for both offset types, and weekends for calendar days)
    let rolled = false;
    const initialDateBeforeRoll = d.toISOString().slice(0,10); // Store date before loop
    while (isSaturday(d) || isSunday(d) || isHoliday(req.jurisdiction, d)) {
      d = addDays(d, 1); // Always roll forward by calendar days
      rolled = true;
    }
    // Only add the roll step if a roll actually happened
    if (rolled) {
       steps.push(`Rolled from ${initialDateBeforeRoll} to next business day → ${d.toISOString().slice(0,10)}`);
    }

    return {
      deadlineCode: dl.code,
      deadlineDate: d.toISOString().slice(0,10),
      ruleCitation: dl.citation,
      calcSteps: steps
    };
  });
}
