#!/bin/bash

# Test script for Core Ailex integration with MCP Rules Engine
# Usage: ./test-core-ailex-integration.sh [staging|production]

ENVIRONMENT=${1:-staging}

if [ "$ENVIRONMENT" = "staging" ]; then
    SERVICE_URL="https://mcp-staging-************.us-central1.run.app"
    API_KEY="mcp-rules-2025-staging"
elif [ "$ENVIRONMENT" = "production" ]; then
    SERVICE_URL="https://mcp-prod-************.us-central1.run.app"
    API_KEY="mcp-rules-2025-production"
else
    echo "Usage: $0 [staging|production]"
    exit 1
fi

echo "🧪 Testing Core Ailex Integration with MCP Rules Engine"
echo "Environment: $ENVIRONMENT"
echo "Service URL: $SERVICE_URL"
echo ""

# Check if service account key exists
if [ ! -f "mcp-client-key.json" ]; then
    echo "❌ Error: mcp-client-key.json not found"
    echo "Please ensure the service account key file is in the current directory"
    exit 1
fi

echo "✅ Service account key found"

# Set environment variable for Google Auth
export GOOGLE_APPLICATION_CREDENTIALS="./mcp-client-key.json"

# Get access token using service account
echo "🔐 Getting access token..."
ACCESS_TOKEN=$(gcloud auth activate-service-account --key-file=mcp-client-key.json --quiet && gcloud auth print-access-token)

if [ -z "$ACCESS_TOKEN" ]; then
    echo "❌ Error: Could not get access token"
    exit 1
fi

echo "✅ Access token obtained"
echo ""

# Test 1: Health check (public endpoint)
echo "🏥 Test 1: Basic health check..."
HEALTH_RESPONSE=$(curl -s -w "%{http_code}" -o /tmp/health_response.json -H "Authorization: Bearer $ACCESS_TOKEN" "$SERVICE_URL/health")
HTTP_CODE=${HEALTH_RESPONSE: -3}

if [ "$HTTP_CODE" = "200" ]; then
    echo "✅ Health check passed"
    echo "Response: $(cat /tmp/health_response.json)"
else
    echo "❌ Health check failed (HTTP $HTTP_CODE)"
    echo "Response: $(cat /tmp/health_response.json)"
fi
echo ""

# Test 2: Detailed health check (protected endpoint)
echo "🔍 Test 2: Detailed health check (with API key)..."
DETAILED_HEALTH_RESPONSE=$(curl -s -w "%{http_code}" -o /tmp/detailed_health_response.json \
    -H "Authorization: Bearer $ACCESS_TOKEN" \
    -H "x-api-key: $API_KEY" \
    "$SERVICE_URL/health/detailed")
HTTP_CODE=${DETAILED_HEALTH_RESPONSE: -3}

if [ "$HTTP_CODE" = "200" ]; then
    echo "✅ Detailed health check passed"
    echo "Response: $(cat /tmp/detailed_health_response.json)"
else
    echo "❌ Detailed health check failed (HTTP $HTTP_CODE)"
    echo "Response: $(cat /tmp/detailed_health_response.json)"
fi
echo ""

# Test 3: Calculate deadlines for Texas arrest
echo "⚖️  Test 3: Calculate deadlines for Texas arrest..."
MCP_RESPONSE=$(curl -s -w "%{http_code}" -o /tmp/mcp_response.json \
    -H "Authorization: Bearer $ACCESS_TOKEN" \
    -H "x-api-key: $API_KEY" \
    -H "Content-Type: application/json" \
    -X POST "$SERVICE_URL/mcp/run" \
    -d '{
        "toolName": "calculate_deadlines",
        "params": {
            "jurisdiction": "TX_STATE",
            "triggerCode": "ARREST_BOOKING",
            "startDate": "2025-01-15"
        }
    }')
HTTP_CODE=${MCP_RESPONSE: -3}

if [ "$HTTP_CODE" = "200" ]; then
    echo "✅ MCP deadline calculation passed"
    echo "Response: $(cat /tmp/mcp_response.json | jq '.' 2>/dev/null || cat /tmp/mcp_response.json)"
else
    echo "❌ MCP deadline calculation failed (HTTP $HTTP_CODE)"
    echo "Response: $(cat /tmp/mcp_response.json)"
fi
echo ""

# Test 4: Test with invalid API key
echo "🔒 Test 4: Testing API key validation..."
INVALID_API_RESPONSE=$(curl -s -w "%{http_code}" -o /tmp/invalid_api_response.json \
    -H "Authorization: Bearer $ACCESS_TOKEN" \
    -H "x-api-key: invalid-key" \
    -H "Content-Type: application/json" \
    -X POST "$SERVICE_URL/mcp/run" \
    -d '{
        "toolName": "calculate_deadlines",
        "params": {
            "jurisdiction": "TX_STATE",
            "triggerCode": "ARREST_BOOKING",
            "startDate": "2025-01-15"
        }
    }')
HTTP_CODE=${INVALID_API_RESPONSE: -3}

if [ "$HTTP_CODE" = "401" ]; then
    echo "✅ API key validation working correctly (rejected invalid key)"
    echo "Response: $(cat /tmp/invalid_api_response.json)"
else
    echo "⚠️  Unexpected response for invalid API key (HTTP $HTTP_CODE)"
    echo "Response: $(cat /tmp/invalid_api_response.json)"
fi
echo ""

# Test 5: Test with invalid jurisdiction
echo "🌍 Test 5: Testing invalid jurisdiction handling..."
INVALID_JURISDICTION_RESPONSE=$(curl -s -w "%{http_code}" -o /tmp/invalid_jurisdiction_response.json \
    -H "Authorization: Bearer $ACCESS_TOKEN" \
    -H "x-api-key: $API_KEY" \
    -H "Content-Type: application/json" \
    -X POST "$SERVICE_URL/mcp/run" \
    -d '{
        "toolName": "calculate_deadlines",
        "params": {
            "jurisdiction": "INVALID_STATE",
            "triggerCode": "ARREST_BOOKING",
            "startDate": "2025-01-15"
        }
    }')
HTTP_CODE=${INVALID_JURISDICTION_RESPONSE: -3}

if [ "$HTTP_CODE" = "404" ] || [ "$HTTP_CODE" = "400" ]; then
    echo "✅ Invalid jurisdiction handling working correctly"
    echo "Response: $(cat /tmp/invalid_jurisdiction_response.json)"
else
    echo "⚠️  Unexpected response for invalid jurisdiction (HTTP $HTTP_CODE)"
    echo "Response: $(cat /tmp/invalid_jurisdiction_response.json)"
fi
echo ""

# Cleanup temporary files
rm -f /tmp/health_response.json /tmp/detailed_health_response.json /tmp/mcp_response.json /tmp/invalid_api_response.json /tmp/invalid_jurisdiction_response.json

echo "🎉 Integration testing complete!"
echo ""
echo "📋 Summary for Core Ailex Integration:"
echo "- Service URL: $SERVICE_URL"
echo "- API Key: $API_KEY"
echo "- Service Account: <EMAIL>"
echo "- Authentication: Google Cloud Run + API Key"
echo ""
echo "📖 Next steps:"
echo "1. Copy mcp-client-key.json to your Core Ailex repository"
echo "2. Add environment variables to Core Ailex .env file"
echo "3. Implement the client code using the examples in CORE_AILEX_INTEGRATION.md"
echo "4. Test the integration in your Core Ailex development environment"
