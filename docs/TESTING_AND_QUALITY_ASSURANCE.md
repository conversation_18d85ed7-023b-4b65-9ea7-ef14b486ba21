# Testing and Quality Assurance Documentation

## Overview

This document outlines the comprehensive testing, quality checks, and audit coverage procedures for the PI Lawyer MCP Rules Engine. These procedures ensure high-quality legal rule extractions and system reliability across all jurisdictions.

## Table of Contents

1. [Coverage Audit System](#coverage-audit-system)
2. [Extraction Quality Checks](#extraction-quality-checks)
3. [Test Suite Documentation](#test-suite-documentation)
4. [Deployment Validation](#deployment-validation)
5. [New Jurisdiction Onboarding](#new-jurisdiction-onboarding)
6. [Continuous Integration](#continuous-integration)

## Coverage Audit System

### 1. PDF Baseline Scanning

**Purpose**: Establish ground truth for deadline coverage expectations

**Tools**:
- `scripts/coverage_audit/pdf_scanner.py` - Scans source PDFs for calendar deadlines
- `scripts/coverage_audit/canonical_checklist.json` - Stores baseline metrics

**Process**:
```bash
# Scan all source PDFs to establish baselines
python scripts/coverage_audit/pdf_scanner.py

# Review baseline metrics
cat scripts/coverage_audit/canonical_checklist.json
```

**Key Metrics**:
- Total calendar deadlines per source
- Deadline density (deadlines per page)
- Source coverage completeness

### 2. Extraction Coverage Analysis

**Purpose**: Compare extracted rules against PDF baselines

**Tools**:
- `scripts/coverage_audit/coverage_audit.py` - Main audit engine
- `scripts/coverage_audit/florida_results_analysis.py` - Florida-specific analysis

**Coverage Targets**:
- **Minimum**: 90% coverage for production readiness
- **Target**: 95%+ coverage for optimal performance
- **Benchmark**: Texas achieved 100%+ coverage

**Process**:
```bash
# Run comprehensive coverage audit
python scripts/coverage_audit/coverage_audit.py

# Generate detailed coverage report
python scripts/coverage_audit/florida_results_analysis.py
```

### 3. Gap Analysis and Resolution

**Identified Gap Types**:
1. **Extraction Gaps**: Rules present in PDF but missing from extraction
2. **Format Gaps**: Rules extracted but in wrong format
3. **Trigger Gaps**: Rules extracted but missing trigger codes

**Resolution Strategies**:
1. **Enhanced Extraction**: Use chunked processing for large documents
2. **Trigger Standardization**: Map to standard trigger codes
3. **Format Conversion**: Convert to rule loader format

## Extraction Quality Checks

### 1. Deduplication Analysis

**Purpose**: Identify and handle duplicate rules

**Expected Metrics**:
- 5-10% duplication rate is normal
- Higher rates indicate extraction issues

**Tools**:
```bash
# Check for duplicates in extracted rules
grep -r "duplicate" scripts/coverage_audit/
```

### 2. Trigger Code Validation

**Purpose**: Ensure consistent trigger codes across jurisdictions

**Standard Trigger Codes**:
- `ARREST_BOOKING` - Date defendant is arrested and booked
- `SERVICE_OF_PROCESS` - Date defendant is served with process
- `PETITION_FILED` - Date petition or complaint is filed
- `MOTION_FILED` - Date motion is filed with court
- `DISCOVERY_REQUEST` - Date discovery request is served
- `TRIAL_SCHEDULED` - Date trial or hearing is scheduled
- `JUDGMENT_ENTERED` - Date judgment or order is entered
- `APPEAL_DEADLINE` - Date triggering appeal deadline
- `ARRAIGNMENT` - Date of arraignment
- `SENTENCING` - Date of sentencing
- `DIVORCE_FILED` - Date divorce petition is filed
- `DIVORCE_DECREED` - Date divorce decree is entered

**Validation Process**:
```bash
# Standardize trigger codes for new jurisdiction
python scripts/coverage_audit/standardize_trigger_codes.py
```

### 3. Holiday Edge Case Testing

**Purpose**: Verify deadline calculations handle holidays correctly

**Test Cases**:
- Deadlines falling on federal holidays
- State-specific holidays
- Weekend roll-forward logic
- Holiday roll-forward logic

**Implementation**:
- Uses `python-holidays` library
- Automatic holiday detection per jurisdiction
- Roll-forward to next business day

### 4. Source Version Tracking

**Purpose**: Enable deterministic diffs and change tracking

**Requirements**:
- All extractions must include `source_version` field
- Format: `YYYY-MM-DD` or ISO timestamp
- Enables comparison between extraction versions

## Test Suite Documentation

### 1. Unit Tests

**Location**: `test/` directory

**Coverage Areas**:
- Rule loading and parsing
- Trigger code validation
- Holiday calculations
- Deadline computations

**Key Test Files**:
- `test/practice-areas/fl-*.spec.ts` - Florida-specific tests
- `test/practice-areas/tx-*.spec.ts` - Texas-specific tests
- `test/comprehensive-rules-test.spec.ts` - Cross-jurisdiction tests
- `test/debug-rules-test.spec.ts` - Debugging utilities

### 2. Integration Tests

**Purpose**: Verify end-to-end functionality

**Test Scenarios**:
- Rule loading across all jurisdictions
- Cross-practice area consistency
- Trigger availability validation
- Holiday handling integration

### 3. Practice Area Tests

**Structure**:
```
test/practice-areas/
├── fl-criminal-defense.spec.ts
├── fl-family-law.spec.ts
├── fl-personal-injury.spec.ts
├── tx-criminal-defense.spec.ts
├── tx-family-law.spec.ts
└── integration.spec.ts
```

**Test Coverage**:
- Trigger code availability
- Deadline calculation accuracy
- Rule format validation
- Cross-jurisdiction consistency

### 4. Test Execution

**Commands**:
```bash
# Run all tests
npm test

# Run specific practice area tests
npm test -- --testPathPattern="fl-criminal"

# Run with verbose output
npm test -- --verbose

# Run specific trigger tests
npm test -- --testNamePattern="ARREST_BOOKING"
```

**Success Criteria**:
- All functional tests passing
- Async logging warnings acceptable (non-functional)
- Core functionality 100% working

## Deployment Validation

### 1. Pre-Deployment Checklist

**Rule Loading Validation**:
- [ ] All jurisdiction files load successfully
- [ ] No placeholder rules in production files
- [ ] All required trigger codes present
- [ ] Metadata fields complete

**Test Suite Validation**:
- [ ] Core functionality tests passing
- [ ] Practice area tests passing
- [ ] Integration tests passing
- [ ] No critical failures

**Coverage Validation**:
- [ ] All practice areas ≥90% coverage
- [ ] Gap analysis complete
- [ ] Quality metrics within targets

### 2. Production Readiness Criteria

**System-Level Requirements**:
- All target jurisdictions functional
- Cross-jurisdiction consistency verified
- Holiday handling tested
- Performance benchmarks met

**Quality Thresholds**:
- Coverage: ≥90% per practice area
- Test Pass Rate: ≥95% functional tests
- Deduplication: 5-10% acceptable range
- Trigger Standardization: 100% compliance

### 3. Post-Deployment Validation

**Monitoring**:
- Rule loading performance
- API response times
- Error rates
- Coverage metrics

**Validation Steps**:
1. Verify all rules load correctly
2. Test sample deadline calculations
3. Validate trigger code functionality
4. Confirm holiday handling

## New Jurisdiction Onboarding

### 1. Source Material Preparation

**Requirements**:
- PDF sources in `rules/{jurisdiction}/sources/` directory
- Source metadata documentation
- Practice area identification

**Quality Checks**:
- PDF readability verification
- Content completeness review
- Version tracking setup

### 2. Extraction Process

**Step 1: PDF Baseline Scanning**
```bash
# Add new jurisdiction to scanner
python scripts/coverage_audit/pdf_scanner.py --jurisdiction NEW_STATE
```

**Step 2: Initial Extraction**
```bash
# Run Gemini 2.0 Flash extraction
python scripts/extraction/{jurisdiction}_{practice_area}_enhanced.py
```

**Step 3: Format Conversion**
```bash
# Convert to rule loader format
python scripts/coverage_audit/convert_{jurisdiction}_to_production.py
```

**Step 4: Trigger Standardization**
```bash
# Standardize trigger codes
python scripts/coverage_audit/standardize_trigger_codes.py
```

### 3. Quality Validation

**Coverage Audit**:
```bash
# Run coverage analysis
python scripts/coverage_audit/coverage_audit.py --jurisdiction NEW_STATE
```

**Test Integration**:
1. Create practice area test files
2. Add jurisdiction to integration tests
3. Verify trigger code availability
4. Test holiday handling

**Success Criteria**:
- ≥90% coverage across all practice areas
- All standard trigger codes available
- Tests passing
- No critical gaps identified

### 4. Documentation Requirements

**Required Documentation**:
- Source material inventory
- Extraction methodology notes
- Coverage analysis results
- Test results summary
- Known limitations/gaps

**File Locations**:
- `docs/jurisdictions/{jurisdiction}/` - Jurisdiction-specific docs
- `rules/{jurisdiction}/README.md` - Source documentation
- Coverage reports in audit results

## Continuous Integration

### 1. Automated Testing

**GitHub Actions Workflow**:
- Trigger on pull requests
- Run full test suite
- Generate coverage reports
- Validate rule loading

**Test Stages**:
1. Unit tests
2. Integration tests
3. Coverage validation
4. Performance benchmarks

### 2. Quality Gates

**Merge Requirements**:
- All tests passing
- Coverage thresholds met
- Code review approved
- Documentation updated

**Deployment Gates**:
- Production readiness checklist complete
- Stakeholder approval
- Rollback plan prepared

### 3. Monitoring and Alerting

**Key Metrics**:
- Test pass rates
- Coverage percentages
- Performance metrics
- Error rates

**Alert Conditions**:
- Test failures
- Coverage drops below threshold
- Performance degradation
- Critical errors

## Tools and Scripts Reference

### Coverage Audit Tools
- `scripts/coverage_audit/pdf_scanner.py` - PDF baseline scanning
- `scripts/coverage_audit/coverage_audit.py` - Main audit engine
- `scripts/coverage_audit/canonical_checklist.json` - Baseline storage

### Extraction Tools
- `scripts/extraction/*_enhanced.py` - Enhanced extraction scripts
- `scripts/coverage_audit/convert_*_to_production.py` - Format conversion
- `scripts/coverage_audit/standardize_trigger_codes.py` - Trigger standardization

### Analysis Tools
- `scripts/coverage_audit/*_results_analysis.py` - Results analysis
- `test/` - Test suite
- `npm test` - Test execution

### Deployment Tools
- `package.json` - Dependencies and scripts
- GitHub Actions - CI/CD pipeline
- Cloud Run deployment scripts

## Best Practices

### 1. Extraction Quality
- Use enhanced extraction for large documents
- Implement chunked processing for better coverage
- Validate trigger codes against standards
- Track source versions for change management

### 2. Testing Strategy
- Test each practice area independently
- Verify cross-jurisdiction consistency
- Include edge cases (holidays, weekends)
- Monitor async operations carefully

### 3. Coverage Management
- Establish baselines before extraction
- Set realistic coverage targets (90%+)
- Document known gaps and limitations
- Plan iterative improvements

### 4. Documentation
- Document all extraction methodologies
- Maintain coverage audit trails
- Update test documentation regularly
- Track quality metrics over time

## Conclusion

This comprehensive testing and quality assurance framework ensures reliable, high-quality legal rule extractions across all jurisdictions. Following these procedures will maintain system integrity and enable confident production deployments.

For questions or updates to these procedures, consult the development team and update this documentation accordingly.
