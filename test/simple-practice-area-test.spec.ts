import { loadRules, getRules, areRulesLoaded, PracticeArea } from '../src/loaders/ruleLoader';

describe("Simple Practice Area Test", () => {
  
  it("should load FL_STATE personal injury rules", async () => {
    await loadRules('FL_STATE', PracticeArea.PERSONAL_INJURY);
    
    expect(areRulesLoaded('FL_STATE', PracticeArea.PERSONAL_INJURY)).toBe(true);
    
    const rules = getRules('FL_STATE', PracticeArea.PERSONAL_INJURY);
    expect(rules).toBeDefined();
    expect(rules.meta).toBeDefined();
    expect(rules.meta.jurisdiction).toBe('FL_STATE');
    expect(rules.meta.practice_area).toBe('Personal Injury');
    expect(rules.triggers).toBeDefined();
    
    console.log('FL Personal Injury triggers count:', rules.triggers?.length);
    console.log('First few triggers:', rules.triggers?.slice(0, 3).map((t: any) => t.code));
  });

  it("should load TX_STATE family law rules", async () => {
    await loadRules('TX_STATE', PracticeArea.FAMILY_LAW);
    
    expect(areRulesLoaded('TX_STATE', PracticeArea.FAMILY_LAW)).toBe(true);
    
    const rules = getRules('TX_STATE', PracticeArea.FAMILY_LAW);
    expect(rules).toBeDefined();
    expect(rules.meta.practice_area).toBe('Family Law');
    
    console.log('TX Family Law triggers count:', rules.triggers?.length);
    console.log('First few triggers:', rules.triggers?.slice(0, 3).map((t: any) => t.code));
  });
});
