import { calculateDeadlines } from "../src/engine/calculator";
import { loadHolidays } from "../src/loaders/holidayLoader"; 
import { loadRules } from '../src/loaders/ruleLoader';

describe("Comprehensive Rules Testing - Florida & Texas", () => {
  // Load holidays and rules once before tests run
  beforeAll(async () => {
    await loadHolidays(2025);
    await loadHolidays(2026); // Test year boundaries
    await loadRules('FL_STATE');
    await loadRules('TX_STATE');
  });

  describe("Florida Civil/Personal Injury Rules", () => {
    it("should calculate FL Rule 1.140(a)(1) - 20 days to serve answer", () => {
      const res = calculateDeadlines({
        jurisdiction: "FL_STATE",
        triggerCode: "SERVICE_OF_PROCESS",
        startDate: "2025-01-15"
      });
      
      expect(res.length).toBeGreaterThan(0);
      expect(res[0].deadlineCode).toBe("ANSWER_DUE");
      expect(res[0].deadlineDate).toBe("2025-02-04"); // 20 calendar days
      expect(res[0].ruleCitation).toContain("1.140");
    });

    it("should handle FL weekend roll-forward for answer deadline", () => {
      // Start on Thursday, 20 days lands on Wednesday (no roll needed)
      const res = calculateDeadlines({
        jurisdiction: "FL_STATE", 
        triggerCode: "SERVICE_OF_PROCESS",
        startDate: "2025-01-16" // Thursday
      });
      
      expect(res[0].deadlineDate).toBe("2025-02-05"); // Wednesday, no roll
    });

    it("should handle FL holiday roll-forward", () => {
      // Test with Martin Luther King Day (Jan 20, 2025)
      const res = calculateDeadlines({
        jurisdiction: "FL_STATE",
        triggerCode: "SERVICE_OF_PROCESS", 
        startDate: "2024-12-31" // 20 days = Jan 20 (MLK Day)
      });
      
      expect(res[0].deadlineDate).toBe("2025-01-21"); // Rolled to Tuesday
      expect(res[0].calcSteps).toContain("Rolled");
    });

    it("should calculate FL Rule 1.140(a)(2)(a) - 40 days for State of Florida", () => {
      const res = calculateDeadlines({
        jurisdiction: "FL_STATE",
        triggerCode: "STATE_ENTITY_SERVICE",
        startDate: "2025-01-15"
      });
      
      expect(res[0].deadlineDate).toBe("2025-02-24"); // 40 calendar days
    });
  });

  describe("Florida Criminal Defense Rules", () => {
    it("should calculate FL Rule 3.181 - 45 days to file death penalty notice", () => {
      const res = calculateDeadlines({
        jurisdiction: "FL_STATE",
        triggerCode: "ARRAIGNMENT",
        startDate: "2025-01-15"
      });
      
      const deathPenaltyDeadline = res.find(d => d.deadlineCode === "DEATH_PENALTY_NOTICE");
      expect(deathPenaltyDeadline).toBeDefined();
      expect(deathPenaltyDeadline?.deadlineDate).toBe("2025-03-01"); // 45 days
    });

    it("should calculate FL Rule 3.190(c) - motion to dismiss before arraignment", () => {
      const res = calculateDeadlines({
        jurisdiction: "FL_STATE",
        triggerCode: "CHARGES_FILED",
        startDate: "2025-01-15"
      });
      
      const motionDeadline = res.find(d => d.deadlineCode === "MOTION_TO_DISMISS");
      expect(motionDeadline).toBeDefined();
    });
  });

  describe("Florida Family Law Rules", () => {
    it("should calculate FL Rule 12.285 - 45 days for mandatory disclosure", () => {
      const res = calculateDeadlines({
        jurisdiction: "FL_STATE",
        triggerCode: "PETITION_SERVICE",
        startDate: "2025-01-15"
      });
      
      const disclosureDeadline = res.find(d => d.deadlineCode === "MANDATORY_DISCLOSURE");
      expect(disclosureDeadline).toBeDefined();
      expect(disclosureDeadline?.deadlineDate).toBe("2025-03-01"); // 45 days
    });

    it("should calculate FL Rule 12.430(b) - 10 days for jury trial demand", () => {
      const res = calculateDeadlines({
        jurisdiction: "FL_STATE",
        triggerCode: "LAST_PLEADING_SERVICE",
        startDate: "2025-01-15"
      });
      
      const juryDeadline = res.find(d => d.deadlineCode === "JURY_TRIAL_DEMAND");
      expect(juryDeadline).toBeDefined();
      expect(juryDeadline?.deadlineDate).toBe("2025-01-25"); // 10 days
    });
  });

  describe("Texas Civil/Personal Injury Rules", () => {
    it("should calculate TX Rule 99(b) - 20 days to serve answer", () => {
      const res = calculateDeadlines({
        jurisdiction: "TX_STATE",
        triggerCode: "SERVICE_OF_PROCESS",
        startDate: "2025-01-15"
      });
      
      expect(res.length).toBeGreaterThan(0);
      expect(res[0].deadlineCode).toBe("ANSWER_DUE");
      expect(res[0].deadlineDate).toBe("2025-02-04"); // 20 calendar days
      expect(res[0].ruleCitation).toContain("99(b)");
    });

    it("should handle TX holiday roll-forward with San Jacinto Day", () => {
      // Test with San Jacinto Day (April 21) - Texas-specific holiday
      const res = calculateDeadlines({
        jurisdiction: "TX_STATE",
        triggerCode: "SERVICE_OF_PROCESS",
        startDate: "2025-04-01" // 20 days = April 21 (San Jacinto Day)
      });
      
      expect(res[0].deadlineDate).toBe("2025-04-22"); // Rolled to Tuesday
      expect(res[0].calcSteps).toContain("Rolled");
    });
  });

  describe("Texas Criminal Defense Rules", () => {
    it("should calculate TX Article 1.051(c)(2) - counsel appointment deadline", () => {
      const res = calculateDeadlines({
        jurisdiction: "TX_STATE",
        triggerCode: "ARREST_BOOKING",
        startDate: "2025-01-15"
      });
      
      const counselDeadline = res.find(d => d.deadlineCode === "COUNSEL_APPOINTMENT");
      expect(counselDeadline).toBeDefined();
      expect(counselDeadline?.deadlineDate).toBe("2025-01-16"); // End of first working day
    });

    it("should calculate TX Article 1.14(b) - objection to indictment before trial", () => {
      const res = calculateDeadlines({
        jurisdiction: "TX_STATE", 
        triggerCode: "CHARGES_FILED",
        startDate: "2025-01-15"
      });
      
      const objectionDeadline = res.find(d => d.deadlineCode === "INDICTMENT_OBJECTION");
      expect(objectionDeadline).toBeDefined();
    });
  });

  describe("Texas Family Law Rules", () => {
    it("should calculate TX Section 6.702 - 60 day waiting period for divorce", () => {
      const res = calculateDeadlines({
        jurisdiction: "TX_STATE",
        triggerCode: "DIVORCE_FILED", 
        startDate: "2025-01-15"
      });
      
      const waitingDeadline = res.find(d => d.deadlineCode === "DIVORCE_WAITING_PERIOD");
      expect(waitingDeadline).toBeDefined();
      expect(waitingDeadline?.deadlineDate).toBe("2025-03-16"); // 60 days
    });

    it("should calculate TX Section 6.801 - 31 day remarriage prohibition", () => {
      const res = calculateDeadlines({
        jurisdiction: "TX_STATE",
        triggerCode: "DIVORCE_DECREED",
        startDate: "2025-01-15"
      });
      
      const remarriageDeadline = res.find(d => d.deadlineCode === "REMARRIAGE_PROHIBITION");
      expect(remarriageDeadline).toBeDefined();
      expect(remarriageDeadline?.deadlineDate).toBe("2025-02-15"); // 31 days
    });
  });

  describe("Cross-Jurisdiction Consistency", () => {
    it("should handle same trigger codes consistently across jurisdictions", () => {
      const flRes = calculateDeadlines({
        jurisdiction: "FL_STATE",
        triggerCode: "SERVICE_OF_PROCESS",
        startDate: "2025-01-15"
      });

      const txRes = calculateDeadlines({
        jurisdiction: "TX_STATE", 
        triggerCode: "SERVICE_OF_PROCESS",
        startDate: "2025-01-15"
      });

      // Both should have answer deadlines
      expect(flRes.length).toBeGreaterThan(0);
      expect(txRes.length).toBeGreaterThan(0);
      
      // Both should be 20 days for basic answer deadline
      expect(flRes[0].deadlineDate).toBe("2025-02-04");
      expect(txRes[0].deadlineDate).toBe("2025-02-04");
    });
  });

  describe("Edge Cases and Error Handling", () => {
    it("should handle invalid jurisdiction gracefully", () => {
      expect(() => {
        calculateDeadlines({
          jurisdiction: "INVALID_STATE",
          triggerCode: "SERVICE_OF_PROCESS", 
          startDate: "2025-01-15"
        });
      }).toThrow();
    });

    it("should handle invalid trigger code gracefully", () => {
      expect(() => {
        calculateDeadlines({
          jurisdiction: "FL_STATE",
          triggerCode: "INVALID_TRIGGER",
          startDate: "2025-01-15"
        });
      }).toThrow();
    });

    it("should handle invalid date format gracefully", () => {
      expect(() => {
        calculateDeadlines({
          jurisdiction: "FL_STATE",
          triggerCode: "SERVICE_OF_PROCESS",
          startDate: "invalid-date"
        });
      }).toThrow();
    });

    it("should handle year boundary calculations", () => {
      const res = calculateDeadlines({
        jurisdiction: "FL_STATE",
        triggerCode: "SERVICE_OF_PROCESS",
        startDate: "2024-12-20" // 20 days crosses into 2025
      });
      
      expect(res[0].deadlineDate).toBe("2025-01-09");
    });

    it("should handle leap year calculations", () => {
      const res = calculateDeadlines({
        jurisdiction: "FL_STATE", 
        triggerCode: "SERVICE_OF_PROCESS",
        startDate: "2024-02-10" // 2024 is a leap year
      });
      
      expect(res[0].deadlineDate).toBe("2024-03-01"); // Should account for Feb 29
    });
  });
});
