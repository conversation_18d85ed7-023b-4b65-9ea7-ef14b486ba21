import { loadRules, getRule, PracticeArea } from '../src/loaders/ruleLoader';
import { mapTriggerCode, getTriggerMapping, isValidTriggerForPracticeArea } from '../src/loaders/triggerCodeMapper';

describe("Trigger Code Mapping", () => {
  
  beforeAll(async () => {
    // Load practice area rules for testing
    await loadRules('FL_STATE', PracticeArea.PERSONAL_INJURY);
    await loadRules('FL_STATE', PracticeArea.CRIMINAL_DEFENSE);
    await loadRules('FL_STATE', PracticeArea.FAMILY_LAW);
    await loadRules('TX_STATE', PracticeArea.CRIMINAL_DEFENSE);
    await loadRules('TX_STATE', PracticeArea.FAMILY_LAW);
  });

  describe("Direct Trigger Code Mappings", () => {
    it("should map SERVICE_OF_PROCESS directly", () => {
      expect(mapTriggerCode('SERVICE_OF_PROCESS')).toBe('SERVICE_OF_PROCESS');
    });

    it("should map ARRAIGNMENT directly", () => {
      expect(mapTriggerCode('ARRAIGNMENT')).toBe('ARRAIGNMENT');
    });

    it("should map CHARGES_FILED directly", () => {
      expect(mapTriggerCode('CHARGES_FILED')).toBe('CHARGES_FILED');
    });
  });

  describe("Mapped Trigger Codes", () => {
    it("should map STATE_ENTITY_SERVICE to SERVICE_OF_PROCESS", () => {
      expect(mapTriggerCode('STATE_ENTITY_SERVICE')).toBe('SERVICE_OF_PROCESS');
    });

    it("should map DIVORCE_FILED to DIVORCE_DECREED", () => {
      expect(mapTriggerCode('DIVORCE_FILED')).toBe('DIVORCE_DECREED');
    });
  });

  describe("Rule Lookup with Mapped Trigger Codes", () => {
    it("should find FL Personal Injury rules using SERVICE_OF_PROCESS", async () => {
      const rule = getRule('FL_STATE', 'SERVICE_OF_PROCESS', PracticeArea.PERSONAL_INJURY);
      
      expect(rule).toBeDefined();
      expect(rule.code).toBe('SERVICE_OF_PROCESS');
      expect(rule.deadlines).toBeDefined();
      expect(rule.deadlines.length).toBeGreaterThan(0);
    });

    it("should find FL Personal Injury rules using mapped STATE_ENTITY_SERVICE", async () => {
      const rule = getRule('FL_STATE', 'STATE_ENTITY_SERVICE', PracticeArea.PERSONAL_INJURY);
      
      expect(rule).toBeDefined();
      expect(rule.code).toBe('SERVICE_OF_PROCESS'); // Should find the actual trigger
      expect(rule.deadlines).toBeDefined();
      expect(rule.deadlines.length).toBeGreaterThan(0);
    });

    it("should find FL Criminal Defense rules using ARRAIGNMENT", async () => {
      const rule = getRule('FL_STATE', 'ARRAIGNMENT', PracticeArea.CRIMINAL_DEFENSE);
      
      expect(rule).toBeDefined();
      expect(rule.code).toBe('ARRAIGNMENT');
      expect(rule.deadlines).toBeDefined();
    });

    it("should find TX Family Law rules using mapped DIVORCE_FILED", async () => {
      const rule = getRule('TX_STATE', 'DIVORCE_FILED', PracticeArea.FAMILY_LAW);
      
      expect(rule).toBeDefined();
      expect(rule.code).toBe('DIVORCE_DECREED'); // Should find the actual trigger
      expect(rule.deadlines).toBeDefined();
    });
  });

  describe("Practice Area Validation", () => {
    it("should validate SERVICE_OF_PROCESS for personal_injury", () => {
      expect(isValidTriggerForPracticeArea('SERVICE_OF_PROCESS', 'personal_injury')).toBe(true);
    });

    it("should validate ARRAIGNMENT for criminal_defense", () => {
      expect(isValidTriggerForPracticeArea('ARRAIGNMENT', 'criminal_defense')).toBe(true);
    });

    it("should not validate ARRAIGNMENT for personal_injury", () => {
      expect(isValidTriggerForPracticeArea('ARRAIGNMENT', 'personal_injury')).toBe(false);
    });
  });

  describe("Trigger Mapping Information", () => {
    it("should provide mapping info for STATE_ENTITY_SERVICE", () => {
      const mapping = getTriggerMapping('STATE_ENTITY_SERVICE');
      
      expect(mapping).toBeDefined();
      expect(mapping?.expectedCode).toBe('STATE_ENTITY_SERVICE');
      expect(mapping?.actualCode).toBe('SERVICE_OF_PROCESS');
      expect(mapping?.practiceAreas).toContain('personal_injury');
    });

    it("should provide mapping info for DIVORCE_FILED", () => {
      const mapping = getTriggerMapping('DIVORCE_FILED');
      
      expect(mapping).toBeDefined();
      expect(mapping?.expectedCode).toBe('DIVORCE_FILED');
      expect(mapping?.actualCode).toBe('DIVORCE_DECREED');
      expect(mapping?.practiceAreas).toContain('family_law');
    });
  });

  describe("Error Handling", () => {
    it("should provide helpful error for invalid trigger code", () => {
      expect(() => {
        getRule('FL_STATE', 'INVALID_TRIGGER', PracticeArea.PERSONAL_INJURY);
      }).toThrow(/not found for.*Available triggers/);
    });

    it("should handle unmapped trigger codes gracefully", () => {
      // Should return the original code if no mapping exists
      expect(mapTriggerCode('UNKNOWN_TRIGGER')).toBe('UNKNOWN_TRIGGER');
    });
  });
});
