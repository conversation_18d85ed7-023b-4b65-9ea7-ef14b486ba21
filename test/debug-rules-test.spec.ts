import { calculateDeadlines } from "../src/engine/calculator";
import { loadRules, getRules } from '../src/loaders/ruleLoader';

describe("Debug Rules Loading", () => {
  beforeAll(async () => {
    await loadRules('FL_STATE');
    await loadRules('TX_STATE');
  });

  it("should load FL_STATE rules correctly", () => {
    const rules = getRules('FL_STATE');
    console.log('FL_STATE rules loaded:', JSON.stringify(rules, null, 2));
    
    expect(rules).toBeDefined();
    expect(rules.meta).toBeDefined();
    expect(rules.triggers).toBeDefined();
    
    console.log('Available FL triggers:', rules.triggers?.map((t: any) => t.code));
  });

  it("should load TX_STATE rules correctly", () => {
    const rules = getRules('TX_STATE');
    console.log('TX_STATE rules loaded:', JSON.stringify(rules, null, 2));
    
    expect(rules).toBeDefined();
    expect(rules.meta).toBeDefined();
    expect(rules.triggers).toBeDefined();
    
    console.log('Available TX triggers:', rules.triggers?.map((t: any) => t.code));
  });

  it("should test FL SERVICE_OF_PROCESS trigger", () => {
    try {
      const res = calculateDeadlines({
        jurisdiction: "FL_STATE",
        triggerCode: "SERVICE_OF_PROCESS",
        startDate: "2025-01-15"
      });
      
      console.log('FL SERVICE_OF_PROCESS result:', JSON.stringify(res, null, 2));
      expect(res.length).toBeGreaterThan(0);
    } catch (error) {
      console.error('FL SERVICE_OF_PROCESS error:', error);
      throw error;
    }
  });

  it("should test TX SERVICE_OF_PROCESS trigger", () => {
    try {
      const res = calculateDeadlines({
        jurisdiction: "TX_STATE",
        triggerCode: "SERVICE_OF_PROCESS",
        startDate: "2025-01-15"
      });
      
      console.log('TX SERVICE_OF_PROCESS result:', JSON.stringify(res, null, 2));
      expect(res.length).toBeGreaterThan(0);
    } catch (error) {
      console.error('TX SERVICE_OF_PROCESS error:', error);
      throw error;
    }
  });
});
