import { loadRules, getRules, areRulesLoaded, getAvailablePracticeAreas, PracticeArea } from '../src/loaders/ruleLoader';

describe("Practice Area Rule Loader", () => {
  
  describe("Practice Area Loading", () => {
    it("should load FL_STATE personal injury rules", async () => {
      await loadRules('FL_STATE', PracticeArea.PERSONAL_INJURY);
      
      expect(areRulesLoaded('FL_STATE', PracticeArea.PERSONAL_INJURY)).toBe(true);
      
      const rules = getRules('FL_STATE', PracticeArea.PERSONAL_INJURY);
      expect(rules).toBeDefined();
      expect(rules.meta).toBeDefined();
      expect(rules.meta.jurisdiction).toBe('FL_STATE');
      expect(rules.meta.practice_area).toBe('Personal Injury');
      expect(rules.triggers).toBeDefined();
      
      console.log('FL Personal Injury triggers:', rules.triggers?.map((t: any) => t.code));
    });

    it("should load FL_STATE family law rules", async () => {
      await loadRules('FL_STATE', PracticeArea.FAMILY_LAW);
      
      expect(areRulesLoaded('FL_STATE', PracticeArea.FAMILY_LAW)).toBe(true);
      
      const rules = getRules('FL_STATE', PracticeArea.FAMILY_LAW);
      expect(rules).toBeDefined();
      expect(rules.meta.practice_area).toBe('Family Law');
    });

    it("should load TX_STATE criminal defense rules", async () => {
      await loadRules('TX_STATE', PracticeArea.CRIMINAL_DEFENSE);
      
      expect(areRulesLoaded('TX_STATE', PracticeArea.CRIMINAL_DEFENSE)).toBe(true);
      
      const rules = getRules('TX_STATE', PracticeArea.CRIMINAL_DEFENSE);
      expect(rules).toBeDefined();
      expect(rules.meta.practice_area).toBe('Criminal Defense');
    });

    it("should load TX_STATE family law rules", async () => {
      await loadRules('TX_STATE', PracticeArea.FAMILY_LAW);
      
      expect(areRulesLoaded('TX_STATE', PracticeArea.FAMILY_LAW)).toBe(true);
      
      const rules = getRules('TX_STATE', PracticeArea.FAMILY_LAW);
      expect(rules).toBeDefined();
      expect(rules.meta.practice_area).toBe('Family Law');
    });
  });

  describe("Practice Area Discovery", () => {
    beforeAll(async () => {
      // Load some practice areas
      await loadRules('FL_STATE', PracticeArea.PERSONAL_INJURY);
      await loadRules('FL_STATE', PracticeArea.FAMILY_LAW);
      await loadRules('TX_STATE', PracticeArea.CRIMINAL_DEFENSE);
    });

    it("should list available practice areas for FL_STATE", () => {
      const practiceAreas = getAvailablePracticeAreas('FL_STATE');
      expect(practiceAreas).toContain('personal_injury');
      expect(practiceAreas).toContain('family_law');
    });

    it("should list available practice areas for TX_STATE", () => {
      const practiceAreas = getAvailablePracticeAreas('TX_STATE');
      expect(practiceAreas).toContain('criminal_defense');
    });
  });

  describe("Error Handling", () => {
    it("should handle non-existent practice area gracefully", async () => {
      await loadRules('FL_STATE', 'non_existent_practice' as any);
      
      expect(areRulesLoaded('FL_STATE', 'non_existent_practice')).toBe(false);
      expect(getRules('FL_STATE', 'non_existent_practice')).toBeUndefined();
    });

    it("should handle non-existent jurisdiction gracefully", async () => {
      await loadRules('INVALID_STATE', PracticeArea.PERSONAL_INJURY);
      
      expect(areRulesLoaded('INVALID_STATE', PracticeArea.PERSONAL_INJURY)).toBe(false);
      expect(getRules('INVALID_STATE', PracticeArea.PERSONAL_INJURY)).toBeUndefined();
    });
  });

  describe("Backward Compatibility", () => {
    it("should still work without practice area (legacy mode)", async () => {
      // This should work if there are general jurisdiction files
      await loadRules('FL_STATE');
      
      // Check if general rules are loaded (this might not exist, which is fine)
      const generalRules = getRules('FL_STATE');
      console.log('General FL rules loaded:', !!generalRules);
    });
  });
});
