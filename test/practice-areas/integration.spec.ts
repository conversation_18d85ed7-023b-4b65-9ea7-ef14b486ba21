import { calculateDeadlines } from "../../src/engine/calculator";
import { loadHolidays } from "../../src/loaders/holidayLoader"; 
import { loadRules, PracticeArea, getAvailablePracticeAreas } from '../../src/loaders/ruleLoader';

describe("Practice Area Integration Tests", () => {
  // Load holidays and all practice area rules before tests run
  beforeAll(async () => {
    await loadHolidays(2025);
    await loadHolidays(2026); // Test year boundaries
    
    // Load all available practice areas
    await loadRules('FL_STATE', PracticeArea.PERSONAL_INJURY);
    await loadRules('FL_STATE', PracticeArea.CRIMINAL_DEFENSE);
    await loadRules('FL_STATE', PracticeArea.FAMILY_LAW);
    await loadRules('TX_STATE', PracticeArea.CRIMINAL_DEFENSE);
    await loadRules('TX_STATE', PracticeArea.FAMILY_LAW);
  });

  describe("Cross-Practice Area Consistency", () => {
    it("should handle same trigger codes consistently across practice areas", () => {
      // Test SERVICE_OF_PROCESS across different practice areas
      const flPersonalInjury = calculateDeadlines({
        jurisdiction: "FL_STATE",
        triggerCode: "SERVICE_OF_PROCESS",
        startDate: "2025-01-15",
        practiceArea: PracticeArea.PERSONAL_INJURY
      });

      const flCriminal = calculateDeadlines({
        jurisdiction: "FL_STATE", 
        triggerCode: "SERVICE_OF_PROCESS",
        startDate: "2025-01-15",
        practiceArea: PracticeArea.CRIMINAL_DEFENSE
      });

      const flFamily = calculateDeadlines({
        jurisdiction: "FL_STATE",
        triggerCode: "SERVICE_OF_PROCESS", 
        startDate: "2025-01-15",
        practiceArea: PracticeArea.FAMILY_LAW
      });

      // All should return deadlines
      expect(flPersonalInjury.length).toBeGreaterThan(0);
      expect(flCriminal.length).toBeGreaterThan(0);
      expect(flFamily.length).toBeGreaterThan(0);

      console.log('SERVICE_OF_PROCESS across FL practice areas:');
      console.log('  Personal Injury:', flPersonalInjury.length, 'deadlines');
      console.log('  Criminal Defense:', flCriminal.length, 'deadlines');
      console.log('  Family Law:', flFamily.length, 'deadlines');
    });

    it("should handle same trigger codes consistently across jurisdictions", () => {
      // Test ARREST_BOOKING across jurisdictions for criminal defense
      const flCriminal = calculateDeadlines({
        jurisdiction: "FL_STATE",
        triggerCode: "ARREST_BOOKING",
        startDate: "2025-01-15",
        practiceArea: PracticeArea.CRIMINAL_DEFENSE
      });

      const txCriminal = calculateDeadlines({
        jurisdiction: "TX_STATE", 
        triggerCode: "ARREST_BOOKING",
        startDate: "2025-01-15",
        practiceArea: PracticeArea.CRIMINAL_DEFENSE
      });

      // Both should return deadlines
      expect(flCriminal.length).toBeGreaterThan(0);
      expect(txCriminal.length).toBeGreaterThan(0);

      console.log('ARREST_BOOKING across jurisdictions:');
      console.log('  FL Criminal:', flCriminal.length, 'deadlines');
      console.log('  TX Criminal:', txCriminal.length, 'deadlines');
    });
  });

  describe("Trigger Code Mapping Integration", () => {
    it("should handle mapped trigger codes across practice areas", () => {
      // Test STATE_ENTITY_SERVICE mapping in personal injury
      const mappedResult = calculateDeadlines({
        jurisdiction: "FL_STATE",
        triggerCode: "STATE_ENTITY_SERVICE", // Maps to SERVICE_OF_PROCESS
        startDate: "2025-01-15",
        practiceArea: PracticeArea.PERSONAL_INJURY
      });

      const directResult = calculateDeadlines({
        jurisdiction: "FL_STATE",
        triggerCode: "SERVICE_OF_PROCESS",
        startDate: "2025-01-15",
        practiceArea: PracticeArea.PERSONAL_INJURY
      });

      // Should return same results since they map to the same trigger
      expect(mappedResult.length).toBe(directResult.length);
      expect(mappedResult[0].deadlineDate).toBe(directResult[0].deadlineDate);

      console.log('Trigger mapping verification:');
      console.log('  STATE_ENTITY_SERVICE:', mappedResult.length, 'deadlines');
      console.log('  SERVICE_OF_PROCESS:', directResult.length, 'deadlines');
    });

    it("should handle DIVORCE_FILED mapping in family law", () => {
      // Test DIVORCE_FILED mapping to DIVORCE_DECREED
      const mappedResult = calculateDeadlines({
        jurisdiction: "TX_STATE",
        triggerCode: "DIVORCE_FILED", // Maps to DIVORCE_DECREED
        startDate: "2025-01-15",
        practiceArea: PracticeArea.FAMILY_LAW
      });

      const directResult = calculateDeadlines({
        jurisdiction: "TX_STATE",
        triggerCode: "DIVORCE_DECREED",
        startDate: "2025-01-15",
        practiceArea: PracticeArea.FAMILY_LAW
      });

      // Should return same results since they map to the same trigger
      expect(mappedResult.length).toBe(directResult.length);
      if (mappedResult.length > 0 && directResult.length > 0) {
        expect(mappedResult[0].deadlineDate).toBe(directResult[0].deadlineDate);
      }

      console.log('DIVORCE trigger mapping verification:');
      console.log('  DIVORCE_FILED:', mappedResult.length, 'deadlines');
      console.log('  DIVORCE_DECREED:', directResult.length, 'deadlines');
    });
  });

  describe("Holiday Handling Across Practice Areas", () => {
    it("should handle Texas-specific holidays consistently", () => {
      // Test San Jacinto Day across different practice areas
      const criminalResult = calculateDeadlines({
        jurisdiction: "TX_STATE",
        triggerCode: "CHARGES_FILED",
        startDate: "2025-04-01", // 20 days = April 21 (San Jacinto Day)
        practiceArea: PracticeArea.CRIMINAL_DEFENSE
      });

      const familyResult = calculateDeadlines({
        jurisdiction: "TX_STATE",
        triggerCode: "EVENT_OCCURS",
        startDate: "2025-04-01",
        practiceArea: PracticeArea.FAMILY_LAW
      });

      // Check if any deadlines were rolled forward due to San Jacinto Day
      const criminalRolled = criminalResult.filter(d => 
        d.calcSteps?.includes("Rolled") && d.deadlineDate.includes("2025-04-22")
      );
      
      const familyRolled = familyResult.filter(d => 
        d.calcSteps?.includes("Rolled") && d.deadlineDate.includes("2025-04-22")
      );

      console.log('San Jacinto Day handling:');
      console.log('  Criminal rolled deadlines:', criminalRolled.length);
      console.log('  Family rolled deadlines:', familyRolled.length);
    });

    it("should handle federal holidays consistently", () => {
      // Test Martin Luther King Day across practice areas
      const flPersonalInjury = calculateDeadlines({
        jurisdiction: "FL_STATE",
        triggerCode: "SERVICE_OF_PROCESS",
        startDate: "2024-12-31", // 20 days = Jan 20 (MLK Day)
        practiceArea: PracticeArea.PERSONAL_INJURY
      });

      const txCriminal = calculateDeadlines({
        jurisdiction: "TX_STATE",
        triggerCode: "ARREST_BOOKING",
        startDate: "2025-01-19", // Next day is MLK Day
        practiceArea: PracticeArea.CRIMINAL_DEFENSE
      });

      // Check for rolled deadlines
      const flRolled = flPersonalInjury.filter(d => d.calcSteps?.includes("Rolled"));
      const txRolled = txCriminal.filter(d => d.calcSteps?.includes("Rolled"));

      console.log('MLK Day handling:');
      console.log('  FL Personal Injury rolled:', flRolled.length);
      console.log('  TX Criminal rolled:', txRolled.length);
    });
  });

  describe("Practice Area Discovery", () => {
    it("should correctly identify available practice areas", () => {
      const flAreas = getAvailablePracticeAreas('FL_STATE');
      const txAreas = getAvailablePracticeAreas('TX_STATE');

      expect(flAreas).toContain('personal_injury');
      expect(flAreas).toContain('criminal_defense');
      expect(flAreas).toContain('family_law');

      expect(txAreas).toContain('criminal_defense');
      expect(txAreas).toContain('family_law');

      console.log('Available practice areas:');
      console.log('  FL_STATE:', flAreas);
      console.log('  TX_STATE:', txAreas);
    });
  });

  describe("Error Handling Integration", () => {
    it("should handle invalid practice area gracefully", () => {
      expect(() => {
        calculateDeadlines({
          jurisdiction: "FL_STATE",
          triggerCode: "SERVICE_OF_PROCESS",
          startDate: "2025-01-15",
          practiceArea: "invalid_practice" as any
        });
      }).toThrow();
    });

    it("should handle trigger code not available in practice area", () => {
      // Try to use a family law specific trigger in personal injury
      expect(() => {
        calculateDeadlines({
          jurisdiction: "TX_STATE",
          triggerCode: "MARRIAGE_CELEBRATED",
          startDate: "2025-01-15",
          practiceArea: PracticeArea.CRIMINAL_DEFENSE // Wrong practice area
        });
      }).toThrow(/not found for.*Available triggers/);
    });
  });

  describe("Performance and Load Testing", () => {
    it("should handle multiple practice area calculations efficiently", () => {
      const startTime = Date.now();

      // Calculate deadlines for multiple practice areas
      const results = [
        calculateDeadlines({
          jurisdiction: "FL_STATE",
          triggerCode: "SERVICE_OF_PROCESS",
          startDate: "2025-01-15",
          practiceArea: PracticeArea.PERSONAL_INJURY
        }),
        calculateDeadlines({
          jurisdiction: "FL_STATE",
          triggerCode: "ARRAIGNMENT",
          startDate: "2025-01-15",
          practiceArea: PracticeArea.CRIMINAL_DEFENSE
        }),
        calculateDeadlines({
          jurisdiction: "TX_STATE",
          triggerCode: "DIVORCE_FILED",
          startDate: "2025-01-15",
          practiceArea: PracticeArea.FAMILY_LAW
        })
      ];

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Should complete within reasonable time (< 1 second)
      expect(duration).toBeLessThan(1000);

      // All should return results
      results.forEach(result => {
        expect(result.length).toBeGreaterThan(0);
      });

      console.log(`Multiple practice area calculations completed in ${duration}ms`);
    });
  });
});
