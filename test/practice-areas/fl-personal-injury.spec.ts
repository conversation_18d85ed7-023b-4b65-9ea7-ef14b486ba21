import { calculateDeadlines } from "../../src/engine/calculator";
import { loadHolidays } from "../../src/loaders/holidayLoader"; 
import { loadRules, PracticeArea } from '../../src/loaders/ruleLoader';

describe("Florida Personal Injury Rules", () => {
  // Load holidays and practice-area-specific rules before tests run
  beforeAll(async () => {
    await loadHolidays(2025);
    await loadHolidays(2026); // Test year boundaries
    await loadRules('FL_STATE', PracticeArea.PERSONAL_INJURY);
  });

  describe("FL Rule 1.140(a)(1) - Answer Deadlines", () => {
    it("should calculate 20 days to serve answer after service of process", () => {
      const res = calculateDeadlines({
        jurisdiction: "FL_STATE",
        triggerCode: "SERVICE_OF_PROCESS",
        startDate: "2025-01-15",
        practiceArea: PracticeArea.PERSONAL_INJURY
      });
      
      expect(res.length).toBeGreaterThan(0);
      
      // Find the answer deadline
      const answerDeadline = res.find(d => 
        d.deadlineCode === "ANSWER_DUE" || 
        d.description?.toLowerCase().includes("answer")
      );
      
      expect(answerDeadline).toBeDefined();
      expect(answerDeadline?.deadlineDate).toBe("2025-02-04"); // 20 calendar days
      
      console.log('FL Personal Injury - SERVICE_OF_PROCESS deadlines:', 
        res.map(d => `${d.deadlineCode}: ${d.deadlineDate} (${d.description})`));
    });

    it("should handle weekend roll-forward for answer deadline", () => {
      // Start on Thursday, 20 days lands on Wednesday (no roll needed)
      const res = calculateDeadlines({
        jurisdiction: "FL_STATE", 
        triggerCode: "SERVICE_OF_PROCESS",
        startDate: "2025-01-16", // Thursday
        practiceArea: PracticeArea.PERSONAL_INJURY
      });
      
      const answerDeadline = res.find(d => 
        d.deadlineCode === "ANSWER_DUE" || 
        d.description?.toLowerCase().includes("answer")
      );
      
      expect(answerDeadline?.deadlineDate).toBe("2025-02-05"); // Wednesday, no roll needed
    });

    it("should handle holiday roll-forward", () => {
      // Test with Martin Luther King Day (Jan 20, 2025)
      const res = calculateDeadlines({
        jurisdiction: "FL_STATE",
        triggerCode: "SERVICE_OF_PROCESS", 
        startDate: "2024-12-31", // 20 days = Jan 20 (MLK Day)
        practiceArea: PracticeArea.PERSONAL_INJURY
      });
      
      const answerDeadline = res.find(d => 
        d.deadlineCode === "ANSWER_DUE" || 
        d.description?.toLowerCase().includes("answer")
      );
      
      expect(answerDeadline?.deadlineDate).toBe("2025-01-21"); // Rolled to Tuesday
      expect(answerDeadline?.calcSteps).toContain("Rolled from");
    });
  });

  describe("FL Rule 1.140(a)(2)(a) - State Entity Service", () => {
    it("should calculate 40 days for State of Florida service", () => {
      const res = calculateDeadlines({
        jurisdiction: "FL_STATE",
        triggerCode: "STATE_ENTITY_SERVICE", // This should map to SERVICE_OF_PROCESS
        startDate: "2025-01-15",
        practiceArea: PracticeArea.PERSONAL_INJURY
      });
      
      // Should find deadlines (mapped trigger code should work)
      expect(res.length).toBeGreaterThan(0);
      
      console.log('FL Personal Injury - STATE_ENTITY_SERVICE deadlines:', 
        res.map(d => `${d.deadlineCode}: ${d.deadlineDate} (${d.description})`));
    });
  });

  describe("Motion Filing Deadlines", () => {
    it("should calculate motion filing deadlines", () => {
      const res = calculateDeadlines({
        jurisdiction: "FL_STATE",
        triggerCode: "MOTION_FILED",
        startDate: "2025-01-15",
        practiceArea: PracticeArea.PERSONAL_INJURY
      });
      
      expect(res.length).toBeGreaterThan(0);
      
      console.log('FL Personal Injury - MOTION_FILED deadlines:', 
        res.map(d => `${d.deadlineCode}: ${d.deadlineDate} (${d.description})`));
    });
  });

  describe("Last Pleading Service Deadlines", () => {
    it("should calculate deadlines after last pleading service", () => {
      const res = calculateDeadlines({
        jurisdiction: "FL_STATE",
        triggerCode: "LAST_PLEADING_SERVICE",
        startDate: "2025-01-15",
        practiceArea: PracticeArea.PERSONAL_INJURY
      });
      
      expect(res.length).toBeGreaterThan(0);
      
      console.log('FL Personal Injury - LAST_PLEADING_SERVICE deadlines:', 
        res.map(d => `${d.deadlineCode}: ${d.deadlineDate} (${d.description})`));
    });
  });

  describe("Edge Cases", () => {
    it("should handle year boundary calculations", () => {
      const res = calculateDeadlines({
        jurisdiction: "FL_STATE",
        triggerCode: "SERVICE_OF_PROCESS",
        startDate: "2024-12-20", // 20 days crosses into 2025
        practiceArea: PracticeArea.PERSONAL_INJURY
      });
      
      const answerDeadline = res.find(d => 
        d.deadlineCode === "ANSWER_DUE" || 
        d.description?.toLowerCase().includes("answer")
      );
      
      expect(answerDeadline?.deadlineDate).toBe("2025-01-09");
    });

    it("should handle leap year calculations", () => {
      const res = calculateDeadlines({
        jurisdiction: "FL_STATE", 
        triggerCode: "SERVICE_OF_PROCESS",
        startDate: "2024-02-10", // 2024 is a leap year
        practiceArea: PracticeArea.PERSONAL_INJURY
      });
      
      const answerDeadline = res.find(d => 
        d.deadlineCode === "ANSWER_DUE" || 
        d.description?.toLowerCase().includes("answer")
      );
      
      expect(answerDeadline?.deadlineDate).toBe("2024-03-01"); // Should account for Feb 29
    });
  });

  describe("Error Handling", () => {
    it("should handle invalid trigger code gracefully", () => {
      expect(() => {
        calculateDeadlines({
          jurisdiction: "FL_STATE",
          triggerCode: "INVALID_TRIGGER",
          startDate: "2025-01-15",
          practiceArea: PracticeArea.PERSONAL_INJURY
        });
      }).toThrow(/not found for.*Available triggers/);
    });

    it("should handle invalid date format gracefully", () => {
      expect(() => {
        calculateDeadlines({
          jurisdiction: "FL_STATE",
          triggerCode: "SERVICE_OF_PROCESS",
          startDate: "invalid-date",
          practiceArea: PracticeArea.PERSONAL_INJURY
        });
      }).toThrow();
    });
  });
});
