import { calculateDeadlines } from "../../src/engine/calculator";
import { loadHolidays } from "../../src/loaders/holidayLoader"; 
import { loadRules, PracticeArea } from '../../src/loaders/ruleLoader';

describe("Texas Family Law Rules", () => {
  // Load holidays and practice-area-specific rules before tests run
  beforeAll(async () => {
    await loadHolidays(2025);
    await loadHolidays(2026); // Test year boundaries
    await loadRules('TX_STATE', PracticeArea.FAMILY_LAW);
  });

  describe("TX Section 6.702 - Divorce Waiting Period", () => {
    it("should calculate 60 day waiting period for divorce", () => {
      const res = calculateDeadlines({
        jurisdiction: "TX_STATE",
        triggerCode: "DIVORCE_FILED", // This should map to DIVORCE_DECREED
        startDate: "2025-01-15",
        practiceArea: PracticeArea.FAMILY_LAW
      });
      
      expect(res.length).toBeGreaterThan(0);
      
      // Look for divorce waiting period deadline
      const waitingDeadline = res.find(d => 
        d.deadlineCode === "DIVORCE_WAITING_PERIOD" ||
        d.description?.toLowerCase().includes("waiting period")
      );
      
      if (waitingDeadline) {
        expect(waitingDeadline.deadlineDate).toBe("2025-03-16"); // 60 days
      }
      
      console.log('TX Family Law - DIVORCE_FILED deadlines:', 
        res.map(d => `${d.deadlineCode}: ${d.deadlineDate} (${d.description})`));
    });
  });

  describe("TX Section 6.801 - Remarriage Prohibition", () => {
    it("should calculate 31 day remarriage prohibition after divorce decree", () => {
      const res = calculateDeadlines({
        jurisdiction: "TX_STATE",
        triggerCode: "DIVORCE_DECREED",
        startDate: "2025-01-15",
        practiceArea: PracticeArea.FAMILY_LAW
      });
      
      expect(res.length).toBeGreaterThan(0);
      
      // Look for remarriage prohibition deadline
      const remarriageDeadline = res.find(d => 
        d.deadlineCode === "REMARRIAGE_PROHIBITION" ||
        d.description?.toLowerCase().includes("remarriage")
      );
      
      if (remarriageDeadline) {
        expect(remarriageDeadline.deadlineDate).toBe("2025-02-15"); // 31 days
      }
      
      console.log('TX Family Law - DIVORCE_DECREED deadlines:', 
        res.map(d => `${d.deadlineCode}: ${d.deadlineDate} (${d.description})`));
    });
  });

  describe("Service of Process in Family Cases", () => {
    it("should calculate service deadlines in family law cases", () => {
      const res = calculateDeadlines({
        jurisdiction: "TX_STATE",
        triggerCode: "SERVICE_OF_PROCESS",
        startDate: "2025-01-15",
        practiceArea: PracticeArea.FAMILY_LAW
      });
      
      expect(res.length).toBeGreaterThan(0);
      
      console.log('TX Family Law - SERVICE_OF_PROCESS deadlines:', 
        res.map(d => `${d.deadlineCode}: ${d.deadlineDate} (${d.description})`));
    });
  });

  describe("Arrest and Booking in Family Cases", () => {
    it("should calculate deadlines after arrest and booking in family cases", () => {
      const res = calculateDeadlines({
        jurisdiction: "TX_STATE",
        triggerCode: "ARREST_BOOKING",
        startDate: "2025-01-15",
        practiceArea: PracticeArea.FAMILY_LAW
      });
      
      expect(res.length).toBeGreaterThan(0);
      
      console.log('TX Family Law - ARREST_BOOKING deadlines:', 
        res.map(d => `${d.deadlineCode}: ${d.deadlineDate} (${d.description})`));
    });
  });

  describe("Marriage Celebration Deadlines", () => {
    it("should calculate deadlines after marriage celebration", () => {
      const res = calculateDeadlines({
        jurisdiction: "TX_STATE",
        triggerCode: "MARRIAGE_CELEBRATED",
        startDate: "2025-01-15",
        practiceArea: PracticeArea.FAMILY_LAW
      });
      
      expect(res.length).toBeGreaterThan(0);
      
      console.log('TX Family Law - MARRIAGE_CELEBRATED deadlines:', 
        res.map(d => `${d.deadlineCode}: ${d.deadlineDate} (${d.description})`));
    });
  });

  describe("General Event Deadlines", () => {
    it("should calculate deadlines for general events in family law", () => {
      const res = calculateDeadlines({
        jurisdiction: "TX_STATE",
        triggerCode: "EVENT_OCCURS",
        startDate: "2025-01-15",
        practiceArea: PracticeArea.FAMILY_LAW
      });
      
      expect(res.length).toBeGreaterThan(0);
      
      console.log('TX Family Law - EVENT_OCCURS deadlines:', 
        res.map(d => `${d.deadlineCode}: ${d.deadlineDate} (${d.description})`));
    });
  });

  describe("Texas-Specific Holiday Handling", () => {
    it("should handle San Jacinto Day roll-forward", () => {
      // Test with San Jacinto Day (April 21) - Texas-specific holiday
      const res = calculateDeadlines({
        jurisdiction: "TX_STATE",
        triggerCode: "DIVORCE_FILED",
        startDate: "2025-02-20", // 60 days = April 21 (San Jacinto Day)
        practiceArea: PracticeArea.FAMILY_LAW
      });
      
      expect(res.length).toBeGreaterThan(0);
      
      // Check if any deadlines were rolled forward due to San Jacinto Day
      const rolledDeadlines = res.filter(d => 
        d.calcSteps?.includes("Rolled") && 
        d.deadlineDate.includes("2025-04-22")
      );
      
      console.log('San Jacinto Day rolled deadlines:', rolledDeadlines.map(d => 
        `${d.deadlineCode}: ${d.deadlineDate} (${d.calcSteps})`));
    });

    it("should handle general holiday roll-forward", () => {
      // Test with Martin Luther King Day (Jan 20, 2025)
      const res = calculateDeadlines({
        jurisdiction: "TX_STATE",
        triggerCode: "DIVORCE_DECREED",
        startDate: "2024-12-20", // 31 days = Jan 20 (MLK Day)
        practiceArea: PracticeArea.FAMILY_LAW
      });
      
      expect(res.length).toBeGreaterThan(0);
      
      // Check if any deadlines were rolled forward
      const rolledDeadlines = res.filter(d => d.calcSteps?.includes("Rolled"));
      console.log('MLK Day rolled deadlines:', rolledDeadlines.map(d => 
        `${d.deadlineCode}: ${d.deadlineDate} (${d.calcSteps})`));
    });
  });

  describe("Edge Cases", () => {
    it("should handle year boundary calculations", () => {
      const res = calculateDeadlines({
        jurisdiction: "TX_STATE",
        triggerCode: "DIVORCE_FILED",
        startDate: "2024-11-15", // 60 days should cross into 2025
        practiceArea: PracticeArea.FAMILY_LAW
      });
      
      expect(res.length).toBeGreaterThan(0);
      
      // Check that deadlines are calculated correctly across year boundary
      const deadlinesIn2025 = res.filter(d => d.deadlineDate.startsWith("2025"));
      expect(deadlinesIn2025.length).toBeGreaterThan(0);
    });

    it("should handle leap year calculations", () => {
      const res = calculateDeadlines({
        jurisdiction: "TX_STATE",
        triggerCode: "DIVORCE_FILED",
        startDate: "2024-01-15", // 2024 is a leap year
        practiceArea: PracticeArea.FAMILY_LAW
      });
      
      expect(res.length).toBeGreaterThan(0);
      
      // Should handle leap year correctly
      const deadlinesInMarch = res.filter(d => d.deadlineDate.includes("2024-03"));
      console.log('Leap year deadlines:', deadlinesInMarch.map(d => 
        `${d.deadlineCode}: ${d.deadlineDate}`));
    });
  });

  describe("Error Handling", () => {
    it("should handle invalid trigger code gracefully", () => {
      expect(() => {
        calculateDeadlines({
          jurisdiction: "TX_STATE",
          triggerCode: "INVALID_FAMILY_TRIGGER",
          startDate: "2025-01-15",
          practiceArea: PracticeArea.FAMILY_LAW
        });
      }).toThrow(/not found for.*Available triggers/);
    });

    it("should provide helpful error messages with available triggers", () => {
      try {
        calculateDeadlines({
          jurisdiction: "TX_STATE",
          triggerCode: "INVALID_TRIGGER",
          startDate: "2025-01-15",
          practiceArea: PracticeArea.FAMILY_LAW
        });
      } catch (error) {
        expect(error.message).toContain("Available triggers");
        expect(error.message).toContain("TX_STATE - family_law");
      }
    });
  });
});
