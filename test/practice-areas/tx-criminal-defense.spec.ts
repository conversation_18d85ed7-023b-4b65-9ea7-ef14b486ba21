import { calculateDeadlines } from "../../src/engine/calculator";
import { loadHolidays } from "../../src/loaders/holidayLoader"; 
import { loadRules, PracticeArea } from '../../src/loaders/ruleLoader';

describe("Texas Criminal Defense Rules", () => {
  // Load holidays and practice-area-specific rules before tests run
  beforeAll(async () => {
    await loadHolidays(2025);
    await loadHolidays(2026); // Test year boundaries
    await loadRules('TX_STATE', PracticeArea.CRIMINAL_DEFENSE);
  });

  describe("TX Article 1.051(c)(2) - Counsel Appointment", () => {
    it("should calculate counsel appointment deadline after arrest and booking", () => {
      const res = calculateDeadlines({
        jurisdiction: "TX_STATE",
        triggerCode: "ARREST_BOOKING",
        startDate: "2025-01-15",
        practiceArea: PracticeArea.CRIMINAL_DEFENSE
      });
      
      expect(res.length).toBeGreaterThan(0);
      
      // Look for counsel appointment deadline
      const counselDeadline = res.find(d => 
        d.deadlineCode === "COUNSEL_APPOINTMENT" ||
        d.description?.toLowerCase().includes("counsel")
      );
      
      if (counselDeadline) {
        expect(counselDeadline.deadlineDate).toBe("2025-01-16"); // End of first working day
      }
      
      console.log('TX Criminal Defense - ARREST_BOOKING deadlines:', 
        res.map(d => `${d.deadlineCode}: ${d.deadlineDate} (${d.description})`));
    });
  });

  describe("TX Article 1.14(b) - Indictment Objection", () => {
    it("should calculate objection to indictment deadline before trial", () => {
      const res = calculateDeadlines({
        jurisdiction: "TX_STATE", 
        triggerCode: "CHARGES_FILED",
        startDate: "2025-01-15",
        practiceArea: PracticeArea.CRIMINAL_DEFENSE
      });
      
      expect(res.length).toBeGreaterThan(0);
      
      // Look for indictment objection deadline
      const objectionDeadline = res.find(d => 
        d.deadlineCode === "INDICTMENT_OBJECTION" ||
        d.description?.toLowerCase().includes("indictment") ||
        d.description?.toLowerCase().includes("objection")
      );
      
      console.log('TX Criminal Defense - CHARGES_FILED deadlines:', 
        res.map(d => `${d.deadlineCode}: ${d.deadlineDate} (${d.description})`));
    });
  });

  describe("Arraignment Deadlines", () => {
    it("should calculate deadlines after arraignment", () => {
      const res = calculateDeadlines({
        jurisdiction: "TX_STATE",
        triggerCode: "ARRAIGNMENT",
        startDate: "2025-01-15",
        practiceArea: PracticeArea.CRIMINAL_DEFENSE
      });
      
      expect(res.length).toBeGreaterThan(0);
      
      console.log('TX Criminal Defense - ARRAIGNMENT deadlines:', 
        res.map(d => `${d.deadlineCode}: ${d.deadlineDate} (${d.description})`));
    });
  });

  describe("Service of Process in Criminal Cases", () => {
    it("should calculate service deadlines in criminal cases", () => {
      const res = calculateDeadlines({
        jurisdiction: "TX_STATE",
        triggerCode: "SERVICE_OF_PROCESS",
        startDate: "2025-01-15",
        practiceArea: PracticeArea.CRIMINAL_DEFENSE
      });
      
      expect(res.length).toBeGreaterThan(0);
      
      console.log('TX Criminal Defense - SERVICE_OF_PROCESS deadlines:', 
        res.map(d => `${d.deadlineCode}: ${d.deadlineDate} (${d.description})`));
    });
  });

  describe("General Event Deadlines", () => {
    it("should calculate deadlines for general events", () => {
      const res = calculateDeadlines({
        jurisdiction: "TX_STATE",
        triggerCode: "EVENT_OCCURS",
        startDate: "2025-01-15",
        practiceArea: PracticeArea.CRIMINAL_DEFENSE
      });
      
      expect(res.length).toBeGreaterThan(0);
      
      console.log('TX Criminal Defense - EVENT_OCCURS deadlines:', 
        res.map(d => `${d.deadlineCode}: ${d.deadlineDate} (${d.description})`));
    });
  });

  describe("Texas-Specific Holiday Handling", () => {
    it("should handle San Jacinto Day roll-forward", () => {
      // Test with San Jacinto Day (April 21) - Texas-specific holiday
      const res = calculateDeadlines({
        jurisdiction: "TX_STATE",
        triggerCode: "CHARGES_FILED",
        startDate: "2025-04-01", // Some deadlines might land on April 21
        practiceArea: PracticeArea.CRIMINAL_DEFENSE
      });
      
      expect(res.length).toBeGreaterThan(0);
      
      // Check if any deadlines were rolled forward due to San Jacinto Day
      const rolledDeadlines = res.filter(d => 
        d.calcSteps?.includes("Rolled") && 
        d.deadlineDate.includes("2025-04-22")
      );
      
      console.log('San Jacinto Day rolled deadlines:', rolledDeadlines.map(d => 
        `${d.deadlineCode}: ${d.deadlineDate} (${d.calcSteps})`));
    });

    it("should handle general holiday roll-forward", () => {
      // Test with Martin Luther King Day (Jan 20, 2025)
      const res = calculateDeadlines({
        jurisdiction: "TX_STATE",
        triggerCode: "ARREST_BOOKING",
        startDate: "2025-01-19", // Next day is MLK Day
        practiceArea: PracticeArea.CRIMINAL_DEFENSE
      });
      
      expect(res.length).toBeGreaterThan(0);
      
      // Check if any deadlines were rolled forward
      const rolledDeadlines = res.filter(d => d.calcSteps?.includes("Rolled"));
      console.log('MLK Day rolled deadlines:', rolledDeadlines.map(d => 
        `${d.deadlineCode}: ${d.deadlineDate} (${d.calcSteps})`));
    });
  });

  describe("Edge Cases", () => {
    it("should handle year boundary calculations", () => {
      const res = calculateDeadlines({
        jurisdiction: "TX_STATE",
        triggerCode: "CHARGES_FILED",
        startDate: "2024-12-20", // Should cross into 2025
        practiceArea: PracticeArea.CRIMINAL_DEFENSE
      });
      
      expect(res.length).toBeGreaterThan(0);
      
      // Check that deadlines are calculated correctly across year boundary
      const deadlinesIn2025 = res.filter(d => d.deadlineDate.startsWith("2025"));
      expect(deadlinesIn2025.length).toBeGreaterThan(0);
    });

    it("should handle business day calculations", () => {
      // Test business day calculations for counsel appointment
      const res = calculateDeadlines({
        jurisdiction: "TX_STATE",
        triggerCode: "ARREST_BOOKING",
        startDate: "2025-01-17", // Friday
        practiceArea: PracticeArea.CRIMINAL_DEFENSE
      });
      
      expect(res.length).toBeGreaterThan(0);
      
      // Counsel appointment should be next business day (Monday)
      const counselDeadline = res.find(d => 
        d.deadlineCode === "COUNSEL_APPOINTMENT" ||
        d.description?.toLowerCase().includes("counsel")
      );
      
      if (counselDeadline) {
        expect(counselDeadline.deadlineDate).toBe("2025-01-20"); // Monday (MLK Day, so Tuesday)
      }
    });
  });

  describe("Error Handling", () => {
    it("should handle invalid trigger code gracefully", () => {
      expect(() => {
        calculateDeadlines({
          jurisdiction: "TX_STATE",
          triggerCode: "INVALID_CRIMINAL_TRIGGER",
          startDate: "2025-01-15",
          practiceArea: PracticeArea.CRIMINAL_DEFENSE
        });
      }).toThrow(/not found for.*Available triggers/);
    });

    it("should provide helpful error messages with available triggers", () => {
      try {
        calculateDeadlines({
          jurisdiction: "TX_STATE",
          triggerCode: "INVALID_TRIGGER",
          startDate: "2025-01-15",
          practiceArea: PracticeArea.CRIMINAL_DEFENSE
        });
      } catch (error) {
        expect(error.message).toContain("Available triggers");
        expect(error.message).toContain("TX_STATE - criminal_defense");
      }
    });
  });
});
