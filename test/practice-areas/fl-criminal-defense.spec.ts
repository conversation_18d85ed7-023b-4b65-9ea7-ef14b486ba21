import { calculateDeadlines } from "../../src/engine/calculator";
import { loadHolidays } from "../../src/loaders/holidayLoader"; 
import { loadRules, PracticeArea } from '../../src/loaders/ruleLoader';

describe("Florida Criminal Defense Rules", () => {
  // Load holidays and practice-area-specific rules before tests run
  beforeAll(async () => {
    await loadHolidays(2025);
    await loadHolidays(2026); // Test year boundaries
    await loadRules('FL_STATE', PracticeArea.CRIMINAL_DEFENSE);
  });

  describe("FL Rule 3.181 - Death Penalty Notice", () => {
    it("should calculate 45 days to file death penalty notice after arraignment", () => {
      const res = calculateDeadlines({
        jurisdiction: "FL_STATE",
        triggerCode: "ARRAIGNMENT",
        startDate: "2025-01-15",
        practiceArea: PracticeArea.CRIMINAL_DEFENSE
      });
      
      expect(res.length).toBeGreaterThan(0);
      
      // Look for death penalty notice deadline
      const deathPenaltyDeadline = res.find(d => 
        d.deadlineCode === "DEATH_PENALTY_NOTICE" ||
        d.description?.toLowerCase().includes("death penalty")
      );
      
      if (deathPenaltyDeadline) {
        expect(deathPenaltyDeadline.deadlineDate).toBe("2025-03-01"); // 45 days
      }
      
      console.log('FL Criminal Defense - ARRAIGNMENT deadlines:', 
        res.map(d => `${d.deadlineCode}: ${d.deadlineDate} (${d.description})`));
    });
  });

  describe("FL Rule 3.190(c) - Motion to Dismiss", () => {
    it("should calculate motion to dismiss deadline before arraignment", () => {
      const res = calculateDeadlines({
        jurisdiction: "FL_STATE",
        triggerCode: "CHARGES_FILED",
        startDate: "2025-01-15",
        practiceArea: PracticeArea.CRIMINAL_DEFENSE
      });
      
      expect(res.length).toBeGreaterThan(0);
      
      // Look for motion to dismiss deadline
      const motionDeadline = res.find(d => 
        d.deadlineCode === "MOTION_TO_DISMISS" ||
        d.description?.toLowerCase().includes("motion to dismiss")
      );
      
      console.log('FL Criminal Defense - CHARGES_FILED deadlines:', 
        res.map(d => `${d.deadlineCode}: ${d.deadlineDate} (${d.description})`));
    });
  });

  describe("Arrest and Booking Deadlines", () => {
    it("should calculate deadlines after arrest and booking", () => {
      const res = calculateDeadlines({
        jurisdiction: "FL_STATE",
        triggerCode: "ARREST_BOOKING",
        startDate: "2025-01-15",
        practiceArea: PracticeArea.CRIMINAL_DEFENSE
      });
      
      expect(res.length).toBeGreaterThan(0);
      
      console.log('FL Criminal Defense - ARREST_BOOKING deadlines:', 
        res.map(d => `${d.deadlineCode}: ${d.deadlineDate} (${d.description})`));
    });
  });

  describe("Service of Process in Criminal Cases", () => {
    it("should calculate service deadlines in criminal cases", () => {
      const res = calculateDeadlines({
        jurisdiction: "FL_STATE",
        triggerCode: "SERVICE_OF_PROCESS",
        startDate: "2025-01-15",
        practiceArea: PracticeArea.CRIMINAL_DEFENSE
      });
      
      expect(res.length).toBeGreaterThan(0);
      
      console.log('FL Criminal Defense - SERVICE_OF_PROCESS deadlines:', 
        res.map(d => `${d.deadlineCode}: ${d.deadlineDate} (${d.description})`));
    });
  });

  describe("General Event Deadlines", () => {
    it("should calculate deadlines for general events", () => {
      const res = calculateDeadlines({
        jurisdiction: "FL_STATE",
        triggerCode: "EVENT_OCCURS",
        startDate: "2025-01-15",
        practiceArea: PracticeArea.CRIMINAL_DEFENSE
      });
      
      expect(res.length).toBeGreaterThan(0);
      
      console.log('FL Criminal Defense - EVENT_OCCURS deadlines:', 
        res.map(d => `${d.deadlineCode}: ${d.deadlineDate} (${d.description})`));
    });
  });

  describe("Holiday and Weekend Handling", () => {
    it("should handle holiday roll-forward in criminal cases", () => {
      // Test with Martin Luther King Day (Jan 20, 2025)
      const res = calculateDeadlines({
        jurisdiction: "FL_STATE",
        triggerCode: "ARRAIGNMENT",
        startDate: "2024-12-06", // 45 days = Jan 20 (MLK Day)
        practiceArea: PracticeArea.CRIMINAL_DEFENSE
      });
      
      expect(res.length).toBeGreaterThan(0);
      
      // Check if any deadlines were rolled forward
      const rolledDeadlines = res.filter(d => d.calcSteps?.includes("Rolled"));
      console.log('Rolled deadlines:', rolledDeadlines.map(d => 
        `${d.deadlineCode}: ${d.deadlineDate} (${d.calcSteps})`));
    });
  });

  describe("Edge Cases", () => {
    it("should handle year boundary calculations", () => {
      const res = calculateDeadlines({
        jurisdiction: "FL_STATE",
        triggerCode: "CHARGES_FILED",
        startDate: "2024-12-20", // Should cross into 2025
        practiceArea: PracticeArea.CRIMINAL_DEFENSE
      });
      
      expect(res.length).toBeGreaterThan(0);
      
      // Check that deadlines are calculated correctly across year boundary
      const deadlinesIn2025 = res.filter(d => d.deadlineDate.startsWith("2025"));
      expect(deadlinesIn2025.length).toBeGreaterThan(0);
    });
  });

  describe("Error Handling", () => {
    it("should handle invalid trigger code gracefully", () => {
      expect(() => {
        calculateDeadlines({
          jurisdiction: "FL_STATE",
          triggerCode: "INVALID_CRIMINAL_TRIGGER",
          startDate: "2025-01-15",
          practiceArea: PracticeArea.CRIMINAL_DEFENSE
        });
      }).toThrow(/not found for.*Available triggers/);
    });

    it("should provide helpful error messages with available triggers", () => {
      try {
        calculateDeadlines({
          jurisdiction: "FL_STATE",
          triggerCode: "INVALID_TRIGGER",
          startDate: "2025-01-15",
          practiceArea: PracticeArea.CRIMINAL_DEFENSE
        });
      } catch (error) {
        expect(error.message).toContain("Available triggers");
        expect(error.message).toContain("FL_STATE - criminal_defense");
      }
    });
  });
});
