import { calculateDeadlines } from "../../src/engine/calculator";
import { loadHolidays } from "../../src/loaders/holidayLoader"; 
import { loadRules, PracticeArea } from '../../src/loaders/ruleLoader';

describe("Florida Family Law Rules", () => {
  // Load holidays and practice-area-specific rules before tests run
  beforeAll(async () => {
    await loadHolidays(2025);
    await loadHolidays(2026); // Test year boundaries
    await loadRules('FL_STATE', PracticeArea.FAMILY_LAW);
  });

  describe("FL Rule 12.285 - Mandatory Disclosure", () => {
    it("should calculate 45 days for mandatory disclosure after petition service", () => {
      const res = calculateDeadlines({
        jurisdiction: "FL_STATE",
        triggerCode: "PETITION_SERVICE",
        startDate: "2025-01-15",
        practiceArea: PracticeArea.FAMILY_LAW
      });
      
      expect(res.length).toBeGreaterThan(0);
      
      // Look for mandatory disclosure deadline
      const disclosureDeadline = res.find(d => 
        d.deadlineCode === "MANDATORY_DISCLOSURE" ||
        d.description?.toLowerCase().includes("mandatory disclosure")
      );
      
      if (disclosureDeadline) {
        expect(disclosureDeadline.deadlineDate).toBe("2025-03-01"); // 45 days
      }
      
      console.log('FL Family Law - PETITION_SERVICE deadlines:', 
        res.map(d => `${d.deadlineCode}: ${d.deadlineDate} (${d.description})`));
    });
  });

  describe("FL Rule 12.430(b) - Jury Trial Demand", () => {
    it("should calculate 10 days for jury trial demand after last pleading service", () => {
      const res = calculateDeadlines({
        jurisdiction: "FL_STATE",
        triggerCode: "LAST_PLEADING_SERVICE",
        startDate: "2025-01-15",
        practiceArea: PracticeArea.FAMILY_LAW
      });
      
      expect(res.length).toBeGreaterThan(0);
      
      // Look for jury trial demand deadline
      const juryDeadline = res.find(d => 
        d.deadlineCode === "JURY_TRIAL_DEMAND" ||
        d.description?.toLowerCase().includes("jury trial")
      );
      
      if (juryDeadline) {
        expect(juryDeadline.deadlineDate).toBe("2025-01-25"); // 10 days
      }
      
      console.log('FL Family Law - LAST_PLEADING_SERVICE deadlines:', 
        res.map(d => `${d.deadlineCode}: ${d.deadlineDate} (${d.description})`));
    });
  });

  describe("Service of Process in Family Cases", () => {
    it("should calculate service deadlines in family law cases", () => {
      const res = calculateDeadlines({
        jurisdiction: "FL_STATE",
        triggerCode: "SERVICE_OF_PROCESS",
        startDate: "2025-01-15",
        practiceArea: PracticeArea.FAMILY_LAW
      });
      
      expect(res.length).toBeGreaterThan(0);
      
      console.log('FL Family Law - SERVICE_OF_PROCESS deadlines:', 
        res.map(d => `${d.deadlineCode}: ${d.deadlineDate} (${d.description})`));
    });
  });

  describe("Arrest and Booking in Family Cases", () => {
    it("should calculate deadlines after arrest and booking in family cases", () => {
      const res = calculateDeadlines({
        jurisdiction: "FL_STATE",
        triggerCode: "ARREST_BOOKING",
        startDate: "2025-01-15",
        practiceArea: PracticeArea.FAMILY_LAW
      });
      
      expect(res.length).toBeGreaterThan(0);
      
      console.log('FL Family Law - ARREST_BOOKING deadlines:', 
        res.map(d => `${d.deadlineCode}: ${d.deadlineDate} (${d.description})`));
    });
  });

  describe("General Event Deadlines", () => {
    it("should calculate deadlines for general events in family law", () => {
      const res = calculateDeadlines({
        jurisdiction: "FL_STATE",
        triggerCode: "EVENT_OCCURS",
        startDate: "2025-01-15",
        practiceArea: PracticeArea.FAMILY_LAW
      });
      
      expect(res.length).toBeGreaterThan(0);
      
      console.log('FL Family Law - EVENT_OCCURS deadlines:', 
        res.map(d => `${d.deadlineCode}: ${d.deadlineDate} (${d.description})`));
    });
  });

  describe("Motion Filing Deadlines", () => {
    it("should calculate motion filing deadlines in family cases", () => {
      const res = calculateDeadlines({
        jurisdiction: "FL_STATE",
        triggerCode: "MOTION_FILED",
        startDate: "2025-01-15",
        practiceArea: PracticeArea.FAMILY_LAW
      });
      
      expect(res.length).toBeGreaterThan(0);
      
      console.log('FL Family Law - MOTION_FILED deadlines:', 
        res.map(d => `${d.deadlineCode}: ${d.deadlineDate} (${d.description})`));
    });
  });

  describe("Holiday and Weekend Handling", () => {
    it("should handle holiday roll-forward in family law cases", () => {
      // Test with Martin Luther King Day (Jan 20, 2025)
      const res = calculateDeadlines({
        jurisdiction: "FL_STATE",
        triggerCode: "PETITION_SERVICE",
        startDate: "2024-12-06", // 45 days = Jan 20 (MLK Day)
        practiceArea: PracticeArea.FAMILY_LAW
      });
      
      expect(res.length).toBeGreaterThan(0);
      
      // Check if any deadlines were rolled forward
      const rolledDeadlines = res.filter(d => d.calcSteps?.includes("Rolled"));
      console.log('Rolled deadlines:', rolledDeadlines.map(d => 
        `${d.deadlineCode}: ${d.deadlineDate} (${d.calcSteps})`));
    });
  });

  describe("Edge Cases", () => {
    it("should handle year boundary calculations", () => {
      const res = calculateDeadlines({
        jurisdiction: "FL_STATE",
        triggerCode: "PETITION_SERVICE",
        startDate: "2024-12-01", // 45 days should cross into 2025
        practiceArea: PracticeArea.FAMILY_LAW
      });
      
      expect(res.length).toBeGreaterThan(0);
      
      // Check that deadlines are calculated correctly across year boundary
      const deadlinesIn2025 = res.filter(d => d.deadlineDate.startsWith("2025"));
      expect(deadlinesIn2025.length).toBeGreaterThan(0);
    });

    it("should handle leap year calculations", () => {
      const res = calculateDeadlines({
        jurisdiction: "FL_STATE",
        triggerCode: "PETITION_SERVICE",
        startDate: "2024-01-15", // 2024 is a leap year
        practiceArea: PracticeArea.FAMILY_LAW
      });
      
      expect(res.length).toBeGreaterThan(0);
      
      // Should handle leap year correctly
      const deadlinesInMarch = res.filter(d => d.deadlineDate.includes("2024-03"));
      console.log('Leap year deadlines:', deadlinesInMarch.map(d => 
        `${d.deadlineCode}: ${d.deadlineDate}`));
    });
  });

  describe("Error Handling", () => {
    it("should handle invalid trigger code gracefully", () => {
      expect(() => {
        calculateDeadlines({
          jurisdiction: "FL_STATE",
          triggerCode: "INVALID_FAMILY_TRIGGER",
          startDate: "2025-01-15",
          practiceArea: PracticeArea.FAMILY_LAW
        });
      }).toThrow(/not found for.*Available triggers/);
    });

    it("should provide helpful error messages with available triggers", () => {
      try {
        calculateDeadlines({
          jurisdiction: "FL_STATE",
          triggerCode: "INVALID_TRIGGER",
          startDate: "2025-01-15",
          practiceArea: PracticeArea.FAMILY_LAW
        });
      } catch (error) {
        expect(error.message).toContain("Available triggers");
        expect(error.message).toContain("FL_STATE - family_law");
      }
    });
  });
});
