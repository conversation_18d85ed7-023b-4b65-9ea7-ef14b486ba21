# 🚀 Quick Start: MCP Rules Engine Deployment

## ⚠️ Permission Issue Resolution

If you encounter storage permission errors during deployment, follow this alternative approach:

## 🔧 **Alternative Deployment Method**

### Option 1: Use Docker Build + Push (Recommended)

```bash
# 1. Build the Docker image locally
docker build -t mcp-rules-engine .

# 2. Create Artifact Registry repository (one-time setup)
gcloud artifacts repositories create mcp \
    --repository-format=docker \
    --location=us-central1 \
    --description="MCP Rules Engine container images"

# 3. Tag for Artifact Registry
docker tag mcp-rules-engine us-central1-docker.pkg.dev/texas-laws-personalinjury/mcp/mcp-rules-engine:latest

# 4. Configure Docker for Artifact Registry
gcloud auth configure-docker us-central1-docker.pkg.dev

# 5. Push the image
docker push us-central1-docker.pkg.dev/texas-laws-personalinjury/mcp/mcp-rules-engine:latest

# 6. Deploy to Cloud Run
gcloud run deploy mcp-staging \
    --image=us-central1-docker.pkg.dev/texas-laws-personalinjury/mcp/mcp-rules-engine:latest \
    --region=us-central1 \
    --platform=managed \
    --allow-unauthenticated \
    --port=4000 \
    --memory=1Gi \
    --cpu=1 \
    --min-instances=0 \
    --max-instances=10 \
    --timeout=300 \
    --concurrency=80 \
    --set-env-vars="NODE_ENV=staging,PORT=4000"
```

### Option 2: Enable Required IAM Permissions

```bash
# Get your project number
PROJECT_NUMBER=$(gcloud projects describe texas-laws-personalinjury --format="value(projectNumber)")

# Grant Cloud Build service account the required permissions
gcloud projects add-iam-policy-binding texas-laws-personalinjury \
    --member="serviceAccount:${PROJECT_NUMBER}-<EMAIL>" \
    --role="roles/storage.admin"

gcloud projects add-iam-policy-binding texas-laws-personalinjury \
    --member="serviceAccount:${PROJECT_NUMBER}-<EMAIL>" \
    --role="roles/cloudbuild.builds.builder"

# Then retry the original deployment
./deployment/cloud-run-deploy.sh staging
```

### Option 3: Use Cloud Shell (No Local Setup Required)

1. Open [Google Cloud Shell](https://shell.cloud.google.com)
2. Clone your repository:
   ```bash
   git clone https://github.com/Jpkay/pi-lawyer-mcp-rules.git
   cd pi-lawyer-mcp-rules
   ```
3. Run the deployment script:
   ```bash
   ./deployment/cloud-run-deploy.sh staging
   ```

## 🧪 **Test Your Deployment**

Once deployed successfully, test with:

```bash
# Get the service URL
SERVICE_URL=$(gcloud run services describe mcp-staging --region=us-central1 --format="value(status.url)")

# Test health endpoint
curl $SERVICE_URL/health

# Test MCP endpoint
curl -X POST $SERVICE_URL/mcp/run \
  -H "Content-Type: application/json" \
  -d '{
    "toolName": "calculate_deadlines",
    "params": {
      "jurisdiction": "FL_STATE",
      "triggerCode": "PLACEHOLDER_TRIGGER",
      "startDate": "2025-01-15"
    }
  }'
```

## 📊 **Expected Response**

Health endpoint should return: `OK`

MCP endpoint should return:
```json
{
  "result": [
    {
      "trigger_code": "PLACEHOLDER_TRIGGER",
      "condition": "default",
      "days": 0,
      "direction": "forward",
      "unit": "calendar_days",
      "description": "Placeholder deadline description"
    }
  ]
}
```

## 🔍 **Troubleshooting**

### Common Issues:

1. **Permission Denied**: Use Option 2 above to grant permissions
2. **Service Not Found**: Ensure you're in the correct project
3. **Build Timeout**: Increase timeout or use pre-built image
4. **Memory Issues**: Increase memory allocation if needed

### Debug Commands:

```bash
# Check service status
gcloud run services describe mcp-staging --region=us-central1

# View logs
gcloud run services logs read mcp-staging --region=us-central1 --limit=50

# List all services
gcloud run services list --region=us-central1
```

## ✅ **Success Criteria**

- ✅ Service deploys without errors
- ✅ Health endpoint returns 200 OK
- ✅ MCP endpoint processes requests
- ✅ Service scales properly
- ✅ Logs show successful rule loading

## 🚀 **Next Steps After Successful Deployment**

1. **Deploy to Production**:
   ```bash
   # Replace 'staging' with 'production' in the commands above
   ```

2. **Set up Monitoring**:
   ```bash
   # Enable Cloud Monitoring
   gcloud services enable monitoring.googleapis.com
   ```

3. **Configure Custom Domain** (Optional):
   ```bash
   gcloud run domain-mappings create --service=mcp-staging --domain=your-domain.com
   ```

4. **Set up CI/CD**: Use the provided GitHub Actions workflow

## 📞 **Need Help?**

If you continue to have issues:
1. Check the deployment logs in Cloud Console
2. Verify your IAM permissions
3. Try the Cloud Shell approach (Option 3)
4. Contact support with the specific error message
