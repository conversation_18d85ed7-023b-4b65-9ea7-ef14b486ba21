# Ignore development files
.env
.env.local
.env.development
.env.test

# Ignore build artifacts that will be rebuilt
node_modules/
dist/

# Ignore development tools
.vscode/
.idea/
*.log
*.tmp

# Ignore test files
test/
*.test.ts
*.spec.ts
jest.config.*

# Ignore documentation
README.md
docs/

# Ignore git files
.git/
.gitignore

# Ignore Python virtual environment
venv/
__pycache__/
*.pyc

# Ignore scripts that aren't needed in production
scripts/debug_*
scripts/examine_*
scripts/test_*
scripts/florida_limited_test.py
scripts/gemini_*
scripts/parse_rules_from_ocr.py

# Ignore source files that aren't needed (we use dist/)
# src/ - Keep this for now as build might need it

# Ignore large rule files that aren't YAML
rules/*.json
rules/*.txt
rules/florida/
rules/Texas/

# Keep only the essential YAML rule files
# rules/*.yaml - Keep these

# Ignore deployment files
deployment/
