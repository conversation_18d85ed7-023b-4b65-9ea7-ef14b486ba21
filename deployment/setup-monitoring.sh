#!/bin/bash

# MCP Rules Engine - Monitoring Setup
# Sets up Cloud Monitoring alerts for production readiness

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Configuration
PROJECT_ID="texas-laws-personalinjury"
REGION="us-central1"

print_status "Setting up monitoring for MCP Rules Engine..."

# Enable required APIs
print_status "Enabling monitoring APIs..."
gcloud services enable monitoring.googleapis.com
gcloud services enable logging.googleapis.com

# Create notification channel (email)
print_status "Setting up notification channels..."
read -p "Enter email for alerts: " ALERT_EMAIL

if [ ! -z "$ALERT_EMAIL" ]; then
    # Create email notification channel
    cat > /tmp/notification-channel.json << EOF
{
  "type": "email",
  "displayName": "MCP Rules Engine Alerts",
  "description": "Email notifications for MCP Rules Engine",
  "labels": {
    "email_address": "$ALERT_EMAIL"
  }
}
EOF

    NOTIFICATION_CHANNEL=$(gcloud alpha monitoring channels create --channel-content-from-file=/tmp/notification-channel.json --format="value(name)")
    print_status "✓ Email notification channel created: $NOTIFICATION_CHANNEL"
    rm /tmp/notification-channel.json
fi

# Create alerting policies
print_status "Creating alerting policies..."

# 1. High latency alert (p99 > 2s)
cat > /tmp/latency-alert.json << EOF
{
  "displayName": "MCP Rules Engine - High Latency",
  "documentation": {
    "content": "Alert when 99th percentile latency exceeds 2 seconds",
    "mimeType": "text/markdown"
  },
  "conditions": [
    {
      "displayName": "Request latency p99 > 2s",
      "conditionThreshold": {
        "filter": "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=~\"mcp-.*\"",
        "comparison": "COMPARISON_GREATER_THAN",
        "thresholdValue": 2000,
        "duration": "300s",
        "aggregations": [
          {
            "alignmentPeriod": "60s",
            "perSeriesAligner": "ALIGN_DELTA",
            "crossSeriesReducer": "REDUCE_PERCENTILE_99",
            "groupByFields": ["resource.labels.service_name"]
          }
        ]
      }
    }
  ],
  "combiner": "OR",
  "enabled": true,
  "notificationChannels": ["$NOTIFICATION_CHANNEL"]
}
EOF

if [ ! -z "$NOTIFICATION_CHANNEL" ]; then
    gcloud alpha monitoring policies create --policy-from-file=/tmp/latency-alert.json
    print_status "✓ High latency alert created"
fi

# 2. High error rate alert (5xx > 1%)
cat > /tmp/error-rate-alert.json << EOF
{
  "displayName": "MCP Rules Engine - High Error Rate",
  "documentation": {
    "content": "Alert when 5xx error rate exceeds 1%",
    "mimeType": "text/markdown"
  },
  "conditions": [
    {
      "displayName": "5xx error rate > 1%",
      "conditionThreshold": {
        "filter": "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=~\"mcp-.*\" AND metric.labels.response_code_class=\"5xx\"",
        "comparison": "COMPARISON_GREATER_THAN",
        "thresholdValue": 0.01,
        "duration": "300s",
        "aggregations": [
          {
            "alignmentPeriod": "60s",
            "perSeriesAligner": "ALIGN_RATE",
            "crossSeriesReducer": "REDUCE_SUM",
            "groupByFields": ["resource.labels.service_name"]
          }
        ]
      }
    }
  ],
  "combiner": "OR",
  "enabled": true,
  "notificationChannels": ["$NOTIFICATION_CHANNEL"]
}
EOF

if [ ! -z "$NOTIFICATION_CHANNEL" ]; then
    gcloud alpha monitoring policies create --policy-from-file=/tmp/error-rate-alert.json
    print_status "✓ High error rate alert created"
fi

# 3. Service down alert
cat > /tmp/service-down-alert.json << EOF
{
  "displayName": "MCP Rules Engine - Service Down",
  "documentation": {
    "content": "Alert when service is not receiving requests",
    "mimeType": "text/markdown"
  },
  "conditions": [
    {
      "displayName": "No requests for 5 minutes",
      "conditionAbsent": {
        "filter": "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=~\"mcp-.*\"",
        "duration": "300s",
        "aggregations": [
          {
            "alignmentPeriod": "60s",
            "perSeriesAligner": "ALIGN_RATE",
            "crossSeriesReducer": "REDUCE_SUM",
            "groupByFields": ["resource.labels.service_name"]
          }
        ]
      }
    }
  ],
  "combiner": "OR",
  "enabled": true,
  "notificationChannels": ["$NOTIFICATION_CHANNEL"]
}
EOF

if [ ! -z "$NOTIFICATION_CHANNEL" ]; then
    gcloud alpha monitoring policies create --policy-from-file=/tmp/service-down-alert.json
    print_status "✓ Service down alert created"
fi

# Clean up temp files
rm -f /tmp/*-alert.json

print_status "✓ Monitoring setup complete!"
print_status "Alerts configured:"
print_status "  - High latency (p99 > 2s)"
print_status "  - High error rate (5xx > 1%)"
print_status "  - Service down (no requests for 5min)"
print_status ""
print_status "View alerts in Cloud Console:"
print_status "https://console.cloud.google.com/monitoring/alerting?project=$PROJECT_ID"
