#!/bin/bash

# MCP Rules Engine - Initial Setup Script
# This script helps set up the Google Cloud environment for deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header "MCP Rules Engine - Cloud Run Setup"

# Check if gcloud is installed
if ! command -v gcloud &> /dev/null; then
    print_error "gcloud CLI is not installed."
    echo "Please install it from: https://cloud.google.com/sdk/docs/install"
    exit 1
fi

print_status "gcloud CLI found"

# Check authentication
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    print_warning "Not authenticated with gcloud"
    echo "Please run: gcloud auth login"
    read -p "Press Enter after authentication..."
fi

# Get current project
CURRENT_PROJECT=$(gcloud config get-value project 2>/dev/null || echo "")

if [ -z "$CURRENT_PROJECT" ]; then
    print_warning "No project set"
    echo "Available projects:"
    gcloud projects list --format="table(projectId,name)"
    echo
    read -p "Enter your project ID: " PROJECT_ID
    gcloud config set project $PROJECT_ID
else
    print_status "Current project: $CURRENT_PROJECT"
    read -p "Use this project? (y/n): " USE_CURRENT
    if [ "$USE_CURRENT" != "y" ]; then
        echo "Available projects:"
        gcloud projects list --format="table(projectId,name)"
        echo
        read -p "Enter your project ID: " PROJECT_ID
        gcloud config set project $PROJECT_ID
    else
        PROJECT_ID=$CURRENT_PROJECT
    fi
fi

print_status "Using project: $PROJECT_ID"

# Enable required APIs
print_status "Enabling required APIs..."
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com

print_status "APIs enabled successfully"

# Check if Docker is available (for local testing)
if command -v docker &> /dev/null; then
    print_status "Docker found - you can test locally"
else
    print_warning "Docker not found - local testing won't be available"
fi

# Create .gcloudignore if it doesn't exist
if [ ! -f .gcloudignore ]; then
    print_status "Creating .gcloudignore file..."
    cp deployment/.gcloudignore .gcloudignore
fi

print_header "Setup Complete!"

echo "Next steps:"
echo "1. Deploy to staging:"
echo "   ./deployment/cloud-run-deploy.sh staging"
echo
echo "2. Test the deployment:"
echo "   ./deployment/test-deployment.sh staging [SERVICE_URL]"
echo
echo "3. Deploy to production:"
echo "   ./deployment/cloud-run-deploy.sh production"
echo
echo "For more information, see: deployment/README.md"
