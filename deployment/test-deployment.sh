#!/bin/bash

# Test script for MCP Rules Engine deployment
# Usage: ./test-deployment.sh [staging|production] [service-url]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_test() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

print_error() {
    echo -e "${RED}[FAIL]${NC} $1"
}

# Check arguments
if [ $# -lt 2 ]; then
    print_error "Usage: $0 [staging|production] [service-url]"
    echo "Example: $0 staging https://mcp-staging-abc123-uc.a.run.app"
    exit 1
fi

ENVIRONMENT=$1
SERVICE_URL=$2

print_status "Testing MCP Rules Engine deployment"
print_status "Environment: $ENVIRONMENT"
print_status "Service URL: $SERVICE_URL"
echo

# Test 1: Health Check
print_test "Testing health endpoint..."
if curl -f -s "$SERVICE_URL/health" > /dev/null; then
    print_success "Health check passed"
else
    print_error "Health check failed"
    exit 1
fi

# Test 2: MCP Endpoint with valid request
print_test "Testing MCP endpoint with valid request..."
RESPONSE=$(curl -s -X POST "$SERVICE_URL/mcp/run" \
    -H "Content-Type: application/json" \
    -d '{
        "toolName": "calculate_deadlines",
        "params": {
            "jurisdiction": "FL_STATE",
            "triggerCode": "PLACEHOLDER_TRIGGER",
            "startDate": "2025-01-15"
        }
    }')

if echo "$RESPONSE" | grep -q "result"; then
    print_success "MCP endpoint responded correctly"
    echo "Response: $RESPONSE"
else
    print_error "MCP endpoint failed"
    echo "Response: $RESPONSE"
    exit 1
fi

# Test 3: Invalid request handling
print_test "Testing invalid request handling..."
RESPONSE=$(curl -s -w "%{http_code}" -X POST "$SERVICE_URL/mcp/run" \
    -H "Content-Type: application/json" \
    -d '{"invalid": "request"}')

HTTP_CODE="${RESPONSE: -3}"
if [ "$HTTP_CODE" = "400" ]; then
    print_success "Invalid request properly rejected with 400"
else
    print_error "Invalid request not handled correctly (got $HTTP_CODE)"
fi

# Test 4: Load test (basic)
print_test "Running basic load test (10 concurrent requests)..."
for i in {1..10}; do
    curl -s -X POST "$SERVICE_URL/mcp/run" \
        -H "Content-Type: application/json" \
        -d '{
            "toolName": "calculate_deadlines",
            "params": {
                "jurisdiction": "FL_STATE",
                "triggerCode": "PLACEHOLDER_TRIGGER",
                "startDate": "2025-01-15"
            }
        }' > /dev/null &
done

wait
print_success "Load test completed"

# Test 5: Response time check
print_test "Checking response time..."
START_TIME=$(date +%s%N)
curl -s "$SERVICE_URL/health" > /dev/null
END_TIME=$(date +%s%N)
RESPONSE_TIME=$(( (END_TIME - START_TIME) / 1000000 ))

if [ $RESPONSE_TIME -lt 1000 ]; then
    print_success "Response time: ${RESPONSE_TIME}ms (good)"
elif [ $RESPONSE_TIME -lt 3000 ]; then
    print_success "Response time: ${RESPONSE_TIME}ms (acceptable)"
else
    print_error "Response time: ${RESPONSE_TIME}ms (slow)"
fi

echo
print_status "All tests completed successfully!"
print_status "Deployment is ready for use."
