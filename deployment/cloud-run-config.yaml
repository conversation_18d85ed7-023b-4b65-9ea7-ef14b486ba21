# Cloud Run Service Configuration Template
# This file documents the configuration used for both staging and production

apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: mcp-staging  # or mcp-prod
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/execution-environment: gen2
spec:
  template:
    metadata:
      annotations:
        # Scaling configuration
        autoscaling.knative.dev/minScale: "0"
        autoscaling.knative.dev/maxScale: "10"
        
        # Resource allocation
        run.googleapis.com/memory: "1Gi"
        run.googleapis.com/cpu: "1"
        
        # Timeout configuration
        run.googleapis.com/timeout: "300s"
        
        # Concurrency
        run.googleapis.com/execution-environment: gen2
        
    spec:
      containerConcurrency: 80
      timeoutSeconds: 300
      containers:
      - image: gcr.io/PROJECT_ID/mcp-rules-engine
        ports:
        - containerPort: 4000
          protocol: TCP
        env:
        - name: PORT
          value: "4000"
        - name: NODE_ENV
          value: "staging"  # or "production"
        resources:
          limits:
            cpu: "1"
            memory: "1Gi"
        livenessProbe:
          httpGet:
            path: /health
            port: 4000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 4000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
