#!/bin/bash

# MCP Rules Engine - Secret Manager Setup
# This script sets up secrets in Google Secret Manager

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
PROJECT_ID="texas-laws-personalinjury"

print_status "Setting up Secret Manager for MCP Rules Engine..."

# Enable Secret Manager API
print_status "Enabling Secret Manager API..."
gcloud services enable secretmanager.googleapis.com

# Check if .env file exists
if [ ! -f .env ]; then
    print_warning ".env file not found. Creating template..."
    cat > .env << EOF
GEMINI_API_KEY=your_gemini_api_key_here
# Add other secrets as needed
# DATABASE_URL=your_database_url_here
# JWT_SECRET=your_jwt_secret_here
EOF
    print_warning "Please edit .env file with your actual secrets, then run this script again."
    exit 1
fi

# Read GEMINI_API_KEY from .env
if [ -f .env ]; then
    export $(grep -v '^#' .env | xargs)
fi

# Create secrets in Secret Manager
print_status "Creating secrets in Secret Manager..."

# GEMINI_API_KEY
if [ ! -z "$GEMINI_API_KEY" ] && [ "$GEMINI_API_KEY" != "your_gemini_api_key_here" ]; then
    print_status "Creating GEMINI_API_KEY secret..."
    echo -n "$GEMINI_API_KEY" | gcloud secrets create gemini-api-key \
        --data-file=- \
        --project=$PROJECT_ID || \
    echo -n "$GEMINI_API_KEY" | gcloud secrets versions add gemini-api-key \
        --data-file=- \
        --project=$PROJECT_ID
    print_status "✓ GEMINI_API_KEY secret created/updated"
else
    print_error "GEMINI_API_KEY not found or not set in .env file"
    exit 1
fi

# Grant Cloud Run service account access to secrets
print_status "Granting Cloud Run access to secrets..."

# Get the project number for the default compute service account
PROJECT_NUMBER=$(gcloud projects describe $PROJECT_ID --format="value(projectNumber)")
COMPUTE_SA="${PROJECT_NUMBER}-<EMAIL>"

# Grant access to secrets
gcloud secrets add-iam-policy-binding gemini-api-key \
    --member="serviceAccount:$COMPUTE_SA" \
    --role="roles/secretmanager.secretAccessor" \
    --project=$PROJECT_ID

print_status "✓ Secret Manager setup complete!"
print_status "Secrets created:"
print_status "  - gemini-api-key"
print_status ""
print_status "Cloud Run services can now access secrets using:"
print_status "  --update-secrets=GEMINI_API_KEY=gemini-api-key:latest"
