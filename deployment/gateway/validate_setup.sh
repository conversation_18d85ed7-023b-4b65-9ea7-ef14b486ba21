#!/bin/bash

# MCP Rules Engine - Setup Validation Script
# Validates that all components are properly configured and ready for deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[✓]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[!]${NC} $1"
}

print_error() {
    echo -e "${RED}[✗]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[VALIDATE]${NC} $1"
}

VALIDATION_PASSED=true

print_header "🔍 MCP Rules Engine - Setup Validation"
echo ""

# Check 1: Required files exist
print_header "📁 Checking required files..."

REQUIRED_FILES=(
    "openapi.yaml"
    "deploy_gateway.sh"
    "create_key.sh"
    "terraform.tf"
    "setup_monitoring.sh"
    "deploy_complete.sh"
    "README.md"
    "monitoring.md"
    "../cloud-run-deploy.sh"
    "../../Dockerfile"
    "../../package.json"
)

for file in "${REQUIRED_FILES[@]}"; do
    if [[ -f "$file" ]]; then
        print_status "Found: $file"
    else
        print_error "Missing: $file"
        VALIDATION_PASSED=false
    fi
done

# Check 2: Scripts are executable
print_header "🔧 Checking script permissions..."

EXECUTABLE_FILES=(
    "deploy_gateway.sh"
    "create_key.sh"
    "setup_monitoring.sh"
    "deploy_complete.sh"
    "../cloud-run-deploy.sh"
)

for file in "${EXECUTABLE_FILES[@]}"; do
    if [[ -x "$file" ]]; then
        print_status "Executable: $file"
    else
        print_warning "Not executable: $file (will be fixed)"
        chmod +x "$file" 2>/dev/null || print_error "Failed to make executable: $file"
    fi
done

# Check 3: gcloud CLI
print_header "☁️  Checking Google Cloud CLI..."

if command -v gcloud &> /dev/null; then
    print_status "gcloud CLI installed"
    
    # Check authentication
    if gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
        ACTIVE_ACCOUNT=$(gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -1)
        print_status "Authenticated as: $ACTIVE_ACCOUNT"
    else
        print_error "Not authenticated with gcloud"
        print_warning "Run: gcloud auth login"
        VALIDATION_PASSED=false
    fi
    
    # Check project
    CURRENT_PROJECT=$(gcloud config get-value project 2>/dev/null || echo "")
    if [[ -n "$CURRENT_PROJECT" ]]; then
        print_status "Current project: $CURRENT_PROJECT"
    else
        print_warning "No project set"
        print_warning "Run: gcloud config set project YOUR_PROJECT_ID"
    fi
else
    print_error "gcloud CLI not installed"
    print_warning "Install from: https://cloud.google.com/sdk/docs/install"
    VALIDATION_PASSED=false
fi

# Check 4: Node.js and npm
print_header "📦 Checking Node.js environment..."

if command -v node &> /dev/null; then
    NODE_VERSION=$(node --version)
    print_status "Node.js installed: $NODE_VERSION"
else
    print_error "Node.js not installed"
    VALIDATION_PASSED=false
fi

if command -v npm &> /dev/null; then
    NPM_VERSION=$(npm --version)
    print_status "npm installed: $NPM_VERSION"
else
    print_error "npm not installed"
    VALIDATION_PASSED=false
fi

# Check 5: Docker (optional)
print_header "🐳 Checking Docker (optional)..."

if command -v docker &> /dev/null; then
    print_status "Docker installed"
else
    print_warning "Docker not installed (optional for local testing)"
fi

# Check 6: Terraform (optional)
print_header "🏗️  Checking Terraform (optional)..."

if command -v terraform &> /dev/null; then
    TERRAFORM_VERSION=$(terraform --version | head -1)
    print_status "Terraform installed: $TERRAFORM_VERSION"
else
    print_warning "Terraform not installed (optional for IaC deployment)"
fi

# Check 7: Configuration files
print_header "⚙️  Checking configuration..."

# Check OpenAPI spec
if [[ -f "openapi.yaml" ]]; then
    if grep -q "rules.ailexlaw.com" openapi.yaml; then
        print_status "OpenAPI spec configured with custom domain"
    else
        print_warning "OpenAPI spec may need domain configuration"
    fi
else
    print_error "OpenAPI specification missing"
    VALIDATION_PASSED=false
fi

# Check project ID in scripts
if grep -q "ailex-rules-prod" deploy_gateway.sh; then
    print_status "Project ID configured in deployment scripts"
else
    print_warning "Project ID may need updating in scripts"
fi

# Check 8: Environment file
print_header "🔐 Checking environment configuration..."

if [[ -f "../../.env" ]]; then
    print_status "Environment file exists"
    if grep -q "GEMINI_API_KEY" "../../.env"; then
        print_status "GEMINI_API_KEY configured"
    else
        print_warning "GEMINI_API_KEY not found in .env"
    fi
else
    print_warning ".env file not found (may be needed for local development)"
fi

# Check 9: Dependencies
print_header "📚 Checking dependencies..."

if [[ -f "../../package.json" ]]; then
    print_status "package.json exists"
    if [[ -d "../../node_modules" ]]; then
        print_status "Dependencies installed"
    else
        print_warning "Dependencies not installed"
        print_warning "Run: npm install"
    fi
else
    print_error "package.json missing"
    VALIDATION_PASSED=false
fi

# Final validation result
echo ""
print_header "📋 VALIDATION SUMMARY"

if [[ "$VALIDATION_PASSED" == "true" ]]; then
    print_status "✅ All critical validations passed!"
    echo ""
    print_header "🚀 READY TO DEPLOY"
    echo "You can now deploy using:"
    echo "  ./deploy_complete.sh production"
    echo "  ./deploy_complete.sh staging"
    echo ""
else
    print_error "❌ Some validations failed"
    echo ""
    print_header "🔧 REQUIRED ACTIONS"
    echo "Please address the errors above before deploying."
    echo ""
fi

print_header "📖 NEXT STEPS"
echo "1. Review configuration in openapi.yaml"
echo "2. Update project IDs if different from 'ailex-rules-prod'"
echo "3. Configure notification email in setup_monitoring.sh"
echo "4. Run deployment: ./deploy_complete.sh [environment]"
echo ""

print_header "📚 DOCUMENTATION"
echo "• Complete guide: README.md"
echo "• Monitoring setup: monitoring.md"
echo "• Deployment summary: DEPLOYMENT_SUMMARY.md"
echo ""

if [[ "$VALIDATION_PASSED" == "true" ]]; then
    exit 0
else
    exit 1
fi
