swagger: '2.0'
info:
  title: MCP Rules Engine API
  description: |
    Production-ready API Gateway for MCP Rules Engine
    
    ## Authentication
    All endpoints require API key authentication via `x-api-key` header.
    
    ## Rate Limiting
    - Default: 1000 requests/minute per API key
    - Burst: 100 requests/10 seconds
    
    ## Environments
    - Production: https://rules.ailexlaw.com
    - Staging: https://staging-rules.ailexlaw.com
  version: '1.0.0'
  contact:
    name: Ailex Law API Support
    email: <EMAIL>
host: rules.ailexlaw.com
basePath: /v1
schemes:
  - https
consumes:
  - application/json
produces:
  - application/json

securityDefinitions:
  ApiKeyAuth:
    type: apiKey
    in: header
    name: x-api-key

security:
  - ApiKeyAuth: []

paths:
  /health:
    get:
      summary: Health Check
      description: Returns the health status of the API
      operationId: healthCheck
      security: []  # No auth required for health check
      responses:
        '200':
          description: Service is healthy
          schema:
            type: object
            properties:
              status:
                type: string
                example: "healthy"
              timestamp:
                type: string
                format: date-time
              version:
                type: string
                example: "1.0.0"
      x-google-backend:
        address: https://mcp-staging-gfunh6mfpa-uc.a.run.app/health
        deadline: 30.0

  /mcp/run:
    post:
      summary: Execute MCP Rules Query
      description: |
        Execute a rules query against the MCP Rules Engine.
        
        ## Request Format
        Send a JSON object with the query parameters.
        
        ## Response Format
        Returns structured legal rules data matching the query criteria.
        
        ## Rate Limits
        - 1000 requests/minute per API key
        - 100 requests/10 seconds burst limit
      operationId: executeMcpQuery
      parameters:
        - name: body
          in: body
          required: true
          schema:
            type: object
            properties:
              jurisdiction:
                type: string
                description: Legal jurisdiction (e.g., "texas", "florida")
                example: "texas"
              category:
                type: string
                description: Rule category (e.g., "civil", "criminal", "family")
                example: "civil"
              query:
                type: string
                description: Natural language query for rules
                example: "statute of limitations for personal injury"
              filters:
                type: object
                description: Additional filters for the query
                properties:
                  effective_date:
                    type: string
                    format: date
                  rule_type:
                    type: string
            required:
              - jurisdiction
      responses:
        '200':
          description: Successful query execution
          schema:
            type: object
            properties:
              success:
                type: boolean
                example: true
              data:
                type: object
                description: Query results
              metadata:
                type: object
                properties:
                  query_time_ms:
                    type: number
                  total_results:
                    type: integer
                  jurisdiction:
                    type: string
        '400':
          description: Bad Request
          schema:
            $ref: '#/definitions/Error'
        '401':
          description: Unauthorized - Invalid API Key
          schema:
            $ref: '#/definitions/Error'
        '429':
          description: Rate Limit Exceeded
          schema:
            $ref: '#/definitions/Error'
        '500':
          description: Internal Server Error
          schema:
            $ref: '#/definitions/Error'
      x-google-backend:
        address: https://mcp-staging-gfunh6mfpa-uc.a.run.app/mcp/run
        deadline: 60.0
        path_translation: APPEND_PATH_TO_ADDRESS
        headers:
          x-api-key: "bypass-gateway-auth"

  /rules/{jurisdiction}:
    get:
      summary: Get Rules by Jurisdiction
      description: Retrieve all rules for a specific jurisdiction
      operationId: getRulesByJurisdiction
      parameters:
        - name: jurisdiction
          in: path
          required: true
          type: string
          description: Legal jurisdiction identifier
        - name: category
          in: query
          type: string
          description: Filter by rule category
        - name: limit
          in: query
          type: integer
          default: 100
          maximum: 1000
          description: Maximum number of results to return
        - name: offset
          in: query
          type: integer
          default: 0
          description: Number of results to skip
      responses:
        '200':
          description: Rules retrieved successfully
          schema:
            type: object
            properties:
              rules:
                type: array
                items:
                  $ref: '#/definitions/Rule'
              pagination:
                $ref: '#/definitions/Pagination'
        '404':
          description: Jurisdiction not found
          schema:
            $ref: '#/definitions/Error'
      x-google-backend:
        address: https://mcp-staging-gfunh6mfpa-uc.a.run.app/rules/{jurisdiction}
        deadline: 30.0

definitions:
  Rule:
    type: object
    properties:
      id:
        type: string
      jurisdiction:
        type: string
      category:
        type: string
      title:
        type: string
      description:
        type: string
      effective_date:
        type: string
        format: date
      source_url:
        type: string
      
  Pagination:
    type: object
    properties:
      total:
        type: integer
      limit:
        type: integer
      offset:
        type: integer
      has_more:
        type: boolean
        
  Error:
    type: object
    properties:
      error:
        type: boolean
        example: true
      message:
        type: string
      code:
        type: string
      details:
        type: object

# Rate limiting quotas
x-google-quota:
  metric_rules:
    - selector: "*"
      metric_costs:
        api_requests: 1
  quotas:
    - name: "api_requests"
      limit: 1000
      unit: "1/min/{project}"
