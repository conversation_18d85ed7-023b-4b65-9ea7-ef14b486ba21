#!/bin/bash

# MCP Rules Engine - Complete Deployment Orchestrator
# This script deploys the entire MCP Rules Engine infrastructure
# Usage: ./deploy_complete.sh [staging|production] [--skip-monitoring]

set -e

# Configuration
PROJECT_ID="texas-laws-personalinjury"
REGION="us-central1"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[DEPLOY]${NC} $1"
}

print_success() {
    echo -e "${PURPLE}[SUCCESS]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [staging|production] [--skip-monitoring]"
    echo ""
    echo "This script performs a complete deployment of the MCP Rules Engine:"
    echo "  1. Validates prerequisites"
    echo "  2. Deploys Cloud Run service"
    echo "  3. Deploys API Gateway"
    echo "  4. Creates API keys"
    echo "  5. Sets up monitoring (unless --skip-monitoring)"
    echo "  6. Runs comprehensive tests"
    echo ""
    echo "Examples:"
    echo "  $0 production                    # Full production deployment"
    echo "  $0 staging --skip-monitoring     # Staging without monitoring"
    echo ""
}

# Parse arguments
ENVIRONMENT=${1:-staging}
SKIP_MONITORING=false

if [[ "$1" == "--help" || "$1" == "-h" ]]; then
    show_usage
    exit 0
fi

if [[ "$ENVIRONMENT" != "staging" && "$ENVIRONMENT" != "production" ]]; then
    print_error "Invalid environment. Use 'staging' or 'production'"
    show_usage
    exit 1
fi

if [[ "$2" == "--skip-monitoring" ]]; then
    SKIP_MONITORING=true
fi

# Start deployment
print_header "🚀 COMPLETE MCP RULES ENGINE DEPLOYMENT"
echo "Environment: $ENVIRONMENT"
echo "Project: $PROJECT_ID"
echo "Region: $REGION"
echo "Skip Monitoring: $SKIP_MONITORING"
echo ""

# Step 1: Prerequisites Check
print_header "📋 Step 1: Checking Prerequisites"

# Check if gcloud is authenticated
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    print_error "Not authenticated with gcloud. Please run 'gcloud auth login'"
    exit 1
fi

# Set the project
gcloud config set project $PROJECT_ID

# Check if required files exist
REQUIRED_FILES=(
    "../cloud-run-deploy.sh"
    "deploy_gateway.sh"
    "create_key.sh"
    "openapi.yaml"
)

for file in "${REQUIRED_FILES[@]}"; do
    if [[ ! -f "$file" ]]; then
        print_error "Required file not found: $file"
        exit 1
    fi
done

print_success "✅ Prerequisites validated"

# Step 2: Deploy Cloud Run Service
print_header "🏃 Step 2: Deploying Cloud Run Service"

cd ..
if ./cloud-run-deploy.sh $ENVIRONMENT; then
    print_success "✅ Cloud Run service deployed successfully"
else
    print_error "❌ Cloud Run deployment failed"
    exit 1
fi
cd gateway

# Step 3: Deploy API Gateway
print_header "🌐 Step 3: Deploying API Gateway"

if ./deploy_gateway.sh $ENVIRONMENT; then
    print_success "✅ API Gateway deployed successfully"
else
    print_error "❌ API Gateway deployment failed"
    exit 1
fi

# Get gateway URL for testing
GATEWAY_URL=$(gcloud api-gateway gateways describe "mcp-rules-gateway-${ENVIRONMENT}" \
    --location=$REGION \
    --project=$PROJECT_ID \
    --format="value(defaultHostname)" 2>/dev/null || echo "")

if [[ -n "$GATEWAY_URL" ]]; then
    GATEWAY_URL="https://$GATEWAY_URL"
    print_status "Gateway URL: $GATEWAY_URL"
else
    print_warning "Could not retrieve gateway URL"
fi

# Step 4: Create API Keys
print_header "🔑 Step 4: Creating API Keys"

# Create API key for Core Ailex
if ./create_key.sh core-ailex $ENVIRONMENT; then
    print_success "✅ Core Ailex API key created"
else
    print_warning "⚠️  API key creation failed or already exists"
fi

# Create test API key
if ./create_key.sh test-client $ENVIRONMENT; then
    print_success "✅ Test client API key created"
else
    print_warning "⚠️  Test API key creation failed or already exists"
fi

# Step 5: Set up Monitoring (optional)
if [[ "$SKIP_MONITORING" == "false" ]]; then
    print_header "📊 Step 5: Setting up Monitoring"
    
    if ./setup_monitoring.sh; then
        print_success "✅ Monitoring configured successfully"
    else
        print_warning "⚠️  Monitoring setup failed - continuing with deployment"
    fi
else
    print_warning "⏭️  Skipping monitoring setup as requested"
fi

# Step 6: Run Tests
print_header "🧪 Step 6: Running Deployment Tests"

# Test health endpoint
if [[ -n "$GATEWAY_URL" ]]; then
    print_status "Testing health endpoint..."
    if curl -f -s "$GATEWAY_URL/health" > /dev/null; then
        print_success "✅ Health check passed"
    else
        print_warning "⚠️  Health check failed - service may still be initializing"
    fi
else
    print_warning "⚠️  Cannot test - gateway URL not available"
fi

# Test with API key (if available)
API_KEY_FILE="api-keys/core-ailex-${ENVIRONMENT}-key-secure.txt"
if [[ -f "$API_KEY_FILE" ]]; then
    API_KEY=$(grep "^API_KEY=" "$API_KEY_FILE" | cut -d'=' -f2)
    if [[ -n "$API_KEY" && -n "$GATEWAY_URL" ]]; then
        print_status "Testing with API key..."
        if curl -f -s -H "x-api-key: $API_KEY" "$GATEWAY_URL/health" > /dev/null; then
            print_success "✅ API key authentication test passed"
        else
            print_warning "⚠️  API key test failed"
        fi
    fi
fi

# Final Summary
print_header "🎉 DEPLOYMENT COMPLETE!"
echo ""
print_success "✅ MCP Rules Engine successfully deployed to $ENVIRONMENT"
echo ""
echo "📋 DEPLOYMENT SUMMARY:"
echo "  • Environment: $ENVIRONMENT"
echo "  • Project: $PROJECT_ID"
echo "  • Region: $REGION"
if [[ -n "$GATEWAY_URL" ]]; then
    echo "  • Gateway URL: $GATEWAY_URL"
fi
echo "  • Cloud Run Service: mcp-${ENVIRONMENT}"
echo "  • API Keys Created: core-ailex, test-client"
if [[ "$SKIP_MONITORING" == "false" ]]; then
    echo "  • Monitoring: Configured"
else
    echo "  • Monitoring: Skipped"
fi
echo ""

print_header "🔗 USEFUL LINKS"
echo "• Google Cloud Console: https://console.cloud.google.com"
echo "• API Gateway: https://console.cloud.google.com/api-gateway"
echo "• Cloud Run: https://console.cloud.google.com/run"
echo "• Monitoring: https://console.cloud.google.com/monitoring"
echo ""

print_header "📋 NEXT STEPS"
echo "1. Configure custom domain mapping (if needed)"
echo "2. Distribute API keys to clients"
echo "3. Set up CI/CD pipelines"
echo "4. Review monitoring dashboards"
echo "5. Test all endpoints thoroughly"
echo ""

print_header "🧪 QUICK TEST"
if [[ -n "$GATEWAY_URL" && -f "$API_KEY_FILE" ]]; then
    echo "Test your deployment:"
    echo "curl -H \"x-api-key: $API_KEY\" \\"
    echo "     \"$GATEWAY_URL/health\""
    echo ""
fi

print_success "🎉 Deployment orchestration completed successfully!"

# Return to original directory
cd ../..
