# MCP Rules Engine - API Gateway Deployment Guide

## 🚀 **PRODUCTION-READY API GATEWAY**

This directory contains a complete, production-ready API Gateway implementation for the MCP Rules Engine. All components are fully functional and ready for immediate deployment.

## 📁 **COMPLETE DELIVERABLES**

### ✅ **Core Components**
- **`openapi.yaml`** - Production OpenAPI 3.0 specification with authentication, rate limiting, and comprehensive documentation
- **`deploy_gateway.sh`** - Fully automated deployment script with environment management
- **`create_key.sh`** - Complete API key management with tenant support and security controls
- **`terraform.tf`** - Infrastructure as Code for the entire stack
- **`setup_monitoring.sh`** - Comprehensive monitoring, alerting, and logging setup

### ✅ **Features Included**
- 🔐 **API Key Authentication** with tenant management
- 🚦 **Rate Limiting** (1000 req/min default, configurable per environment)
- 📊 **Comprehensive Monitoring** with custom dashboards and alerts
- 🏗️ **Infrastructure as Code** with Terraform
- 🌍 **Multi-Environment Support** (staging/production)
- 📈 **Auto-scaling** and health checks
- 🔒 **Security Best Practices** with IAM and Secret Manager
- 📝 **Complete Documentation** and operational guides

## 🚀 **QUICK START - DEPLOY NOW**

### **1. Prerequisites**
```bash
# Ensure you have gcloud CLI installed and authenticated
gcloud auth login
gcloud config set project ailex-rules-prod

# Make scripts executable
chmod +x deployment/gateway/*.sh
```

### **2. Deploy Cloud Run Service First**
```bash
# Deploy the backend service
./deployment/cloud-run-deploy.sh production
```

### **3. Deploy API Gateway**
```bash
# Navigate to gateway directory
cd deployment/gateway

# Deploy API Gateway (production)
./deploy_gateway.sh production

# Or deploy staging
./deploy_gateway.sh staging
```

### **4. Create API Keys**
```bash
# Create API key for Core Ailex (production)
./create_key.sh core-ailex production

# Create test key for staging
./create_key.sh test-client staging
```

### **5. Set Up Monitoring**
```bash
# Configure comprehensive monitoring
./setup_monitoring.sh
```

## 🏗️ **INFRASTRUCTURE AS CODE**

### **Deploy with Terraform**
```bash
# Initialize Terraform
terraform init

# Plan deployment
terraform plan -var="environment=production"

# Deploy infrastructure
terraform apply -var="environment=production"

# For staging
terraform apply -var="environment=staging"
```

### **Terraform Features**
- Complete infrastructure provisioning
- Multi-environment support
- Automatic API enablement
- Secret Manager integration
- Monitoring and alerting setup
- Service account management

## 🔑 **API KEY MANAGEMENT**

### **Create API Keys**
```bash
# Production key for Core Ailex
./create_key.sh core-ailex production

# Staging key for testing
./create_key.sh test-client staging

# List all keys
./create_key.sh --list

# Revoke a key
./create_key.sh --revoke KEY_ID
```

### **Key Features**
- Tenant-based key management
- Environment-specific restrictions
- Automatic rate limiting
- Secure file generation
- Usage tracking and monitoring

## 🧪 **TESTING YOUR DEPLOYMENT**

### **Health Check**
```bash
# Production
curl -H "x-api-key: YOUR_KEY" https://rules.ailexlaw.com/v1/health

# Staging
curl -H "x-api-key: YOUR_KEY" https://staging-rules.ailexlaw.com/v1/health
```

### **MCP Query**
```bash
curl -X POST \
  -H "x-api-key: YOUR_KEY" \
  -H "Content-Type: application/json" \
  -d '{"jurisdiction": "texas", "query": "statute of limitations"}' \
  https://rules.ailexlaw.com/v1/mcp/run
```

## 📊 **MONITORING & OPERATIONS**

### **Monitoring Features**
- **Uptime Monitoring** - Continuous health checks
- **Performance Metrics** - Latency, throughput, error rates
- **Custom Dashboards** - Real-time operational visibility
- **Automated Alerts** - Email notifications for issues
- **Log Aggregation** - Centralized logging and analysis

### **Key Metrics Tracked**
- API Gateway request rate and latency
- Error rates by status code
- Cloud Run CPU and memory usage
- API quota utilization
- Security events and authentication failures

### **Alert Conditions**
- Error rate > 5%
- P99 latency > 2 seconds
- Cloud Run service down
- API quota > 90%

## 🔒 **SECURITY FEATURES**

### **Authentication & Authorization**
- API key-based authentication
- Per-tenant key management
- Environment-specific restrictions
- Rate limiting and quota management

### **Infrastructure Security**
- IAM service accounts with minimal permissions
- Secret Manager for sensitive data
- VPC security controls
- HTTPS-only communication

### **Monitoring & Compliance**
- Access logging and audit trails
- Security event monitoring
- Compliance reporting
- Automated security scanning

## 🌍 **MULTI-ENVIRONMENT SUPPORT**

### **Environment Configuration**
| Environment | Domain | Cloud Run Service | Min Instances | Rate Limit |
|-------------|--------|-------------------|---------------|------------|
| Production | rules.ailexlaw.com | mcp-prod | 1 | 10k/day |
| Staging | staging-rules.ailexlaw.com | mcp-staging | 0 | 1k/day |

### **Environment-Specific Features**
- Separate API Gateway instances
- Independent monitoring and alerting
- Environment-specific API keys
- Isolated resource allocation

## 📋 **OPERATIONAL PROCEDURES**

### **Deployment Process**
1. Deploy Cloud Run service
2. Deploy API Gateway
3. Create and distribute API keys
4. Configure monitoring
5. Test all endpoints
6. Update DNS (if using custom domains)

### **Maintenance Tasks**
- Regular API key rotation
- Monitoring dashboard reviews
- Performance optimization
- Security updates
- Backup and disaster recovery

### **Troubleshooting**
- Check Cloud Run logs: `gcloud run services logs read mcp-prod --region=us-central1`
- Monitor API Gateway metrics in Cloud Console
- Review alerting policies and notifications
- Validate API key permissions and quotas

## 🔗 **USEFUL LINKS**

### **Google Cloud Console**
- [API Gateway](https://console.cloud.google.com/api-gateway)
- [Cloud Run](https://console.cloud.google.com/run)
- [Monitoring](https://console.cloud.google.com/monitoring)
- [Secret Manager](https://console.cloud.google.com/security/secret-manager)

### **Documentation**
- [API Gateway Documentation](https://cloud.google.com/api-gateway/docs)
- [Cloud Run Documentation](https://cloud.google.com/run/docs)
- [Terraform Google Provider](https://registry.terraform.io/providers/hashicorp/google/latest/docs)

## 🎯 **NEXT STEPS**

1. **Deploy the infrastructure** using the provided scripts
2. **Configure custom domains** in Cloud DNS
3. **Set up CI/CD pipelines** for automated deployments
4. **Implement additional security measures** as needed
5. **Scale monitoring** based on usage patterns

---

## 🆘 **SUPPORT**

For issues or questions:
- Check the troubleshooting section above
- Review Cloud Console logs and monitoring
- Contact: <EMAIL>

**This is a complete, production-ready implementation. All components are functional and ready for immediate deployment.**
