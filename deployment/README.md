# MCP Rules Engine - Cloud Run Deployment Guide

## Overview

This directory contains deployment configurations and scripts for deploying the MCP Rules Engine to Google Cloud Run with separate staging and production environments.

## Architecture

```
┌─────────────────┐    ┌─────────────────┐
│   Staging       │    │   Production    │
│   mcp-staging   │    │   mcp-prod      │
│   us-central1   │    │   us-central1   │
└─────────────────┘    └─────────────────┘
         │                       │
         └───────────────────────┘
                     │
            ┌─────────────────┐
            │  Load Balancer  │
            │  (Optional)     │
            └─────────────────┘
```

## Prerequisites

1. **Google Cloud CLI**: Install and authenticate
   ```bash
   # Install gcloud CLI
   curl https://sdk.cloud.google.com | bash
   
   # Authenticate
   gcloud auth login
   gcloud auth application-default login
   ```

2. **Project Setup**: Ensure you have a Google Cloud project
   ```bash
   gcloud config set project YOUR_PROJECT_ID
   ```

3. **Required APIs**: The deployment script will enable these automatically
   - Cloud Run API
   - Cloud Build API

## Quick Deployment

### 1. Deploy to Staging
```bash
# Make scripts executable
chmod +x deployment/cloud-run-deploy.sh
chmod +x deployment/test-deployment.sh

# Deploy to staging
./deployment/cloud-run-deploy.sh staging
```

### 2. Test Staging Deployment
```bash
# Get the staging URL from the deployment output, then test
./deployment/test-deployment.sh staging https://mcp-staging-xxx-uc.a.run.app
```

### 3. Deploy to Production
```bash
# After staging tests pass
./deployment/cloud-run-deploy.sh production
```

## Manual Deployment Commands

If you prefer manual deployment:

### Staging
```bash
gcloud run deploy mcp-staging \
    --source=. \
    --region=us-central1 \
    --platform=managed \
    --allow-unauthenticated \
    --port=4000 \
    --memory=1Gi \
    --cpu=1 \
    --min-instances=0 \
    --max-instances=10 \
    --timeout=300 \
    --concurrency=80 \
    --set-env-vars="NODE_ENV=staging,PORT=4000" \
    --tag=staging
```

### Production
```bash
gcloud run deploy mcp-prod \
    --source=. \
    --region=us-central1 \
    --platform=managed \
    --allow-unauthenticated \
    --port=4000 \
    --memory=1Gi \
    --cpu=1 \
    --min-instances=1 \
    --max-instances=20 \
    --timeout=300 \
    --concurrency=80 \
    --set-env-vars="NODE_ENV=production,PORT=4000" \
    --tag=prod
```

## Configuration

### Environment Variables
- `NODE_ENV`: Environment (staging/production)
- `PORT`: Server port (4000)
- `GEMINI_API_KEY`: Set via Secret Manager (recommended)

### Resource Allocation
- **Memory**: 1Gi (sufficient for rule loading)
- **CPU**: 1 vCPU
- **Concurrency**: 80 requests per instance
- **Timeout**: 300 seconds

## Monitoring & Logging

### View Logs
```bash
# Staging logs
gcloud run services logs read mcp-staging --region=us-central1 --limit=50

# Production logs
gcloud run services logs read mcp-prod --region=us-central1 --limit=50
```

### Health Checks
- Health endpoint: `GET /health`
- Returns: `200 OK` with "OK" body

## Testing

The deployment includes comprehensive testing:

1. **Health Check**: Verifies service is running
2. **MCP Endpoint**: Tests deadline calculation
3. **Error Handling**: Validates error responses
4. **Load Testing**: Basic concurrent request testing
5. **Performance**: Response time validation

## Security Considerations

1. **Authentication**: Currently allows unauthenticated access
2. **CORS**: Configure if needed for browser access
3. **Rate Limiting**: Consider implementing for production
4. **Secrets**: Use Google Secret Manager for sensitive data

## Scaling Configuration

### Staging
- Min instances: 0 (cost-effective)
- Max instances: 10
- Scale to zero when idle

### Production
- Min instances: 1 (faster response)
- Max instances: 20
- Always warm instance available

## Cost Optimization

1. **Staging**: Scales to zero when not in use
2. **Production**: Minimal warm instances
3. **Resource allocation**: Right-sized for workload
4. **Regional deployment**: Single region (us-central1)

## Troubleshooting

### Common Issues

1. **Build Failures**
   ```bash
   # Check build logs
   gcloud builds list --limit=5
   gcloud builds log BUILD_ID
   ```

2. **Service Not Starting**
   ```bash
   # Check service logs
   gcloud run services logs read SERVICE_NAME --region=us-central1
   ```

3. **Permission Issues**
   ```bash
   # Ensure proper IAM roles
   gcloud projects add-iam-policy-binding PROJECT_ID \
       --member="user:YOUR_EMAIL" \
       --role="roles/run.admin"
   ```

## Next Steps

1. **Load Balancer**: Add Cloud Load Balancing for production
2. **CDN**: Consider Cloud CDN for static assets
3. **Monitoring**: Set up Cloud Monitoring alerts
4. **CI/CD**: Integrate with GitHub Actions
5. **Database**: Add persistent storage if needed
